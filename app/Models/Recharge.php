<?php

namespace App\Models;

use App\Models\UsersBank;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;

class Recharge extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'recharge';

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'agent_id');
    }

    public function bank()
    {
        return $this->belongsTo(UsersBank::class, 'bank_id', 'id');
    }
}
