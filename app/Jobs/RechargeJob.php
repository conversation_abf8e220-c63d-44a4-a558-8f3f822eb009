<?php


namespace App\Jobs;

use Exception;
use App\Models\Kq;
use App\Models\User;
use App\Models\UsersBank;
use App\Models\PayChannel;
use App\Models\UserBbBank;
use App\Models\RechargeList;
use Illuminate\Bus\Queueable;
use App\Services\UserUpAmount;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use App\Http\Controllers\HuiFuController;
use App\Models\Recharge as RechargeModel;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Http\Controllers\kq\PayBankController;
use App\Http\Controllers\alipay\AliPayController;
use Illuminate\Contracts\Cache\LockTimeoutException;
use App\Http\Controllers\AqPay\PaySecurityController;

class RechargeJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $id;
    protected $params;
    protected $business;

    public function __construct(int $id, int $business, array $params = [])
    {
        $this->id = $id;
        $this->params = $params;
    }

    public function handle(): void
    {

        try {
            Cache::lock(__CLASS__ . __FUNCTION__ . $this->business, 30)->block(60, function () {
                $this->runTask();
            });
        } catch (LockTimeoutException | Exception $exception) {
            Log::channel('recharge')->info('商户创建订单失败 ', [
                'line' => $exception->getLine(),
                'file' => $exception->getFile(),
                'code' => $exception->getCode(),
                'message' => $exception->getMessage(),
            ]);
        }
    }

    public function runTask()
    {
        $lock = Cache::lock(__CLASS__ . __FUNCTION__ . 'id:' . $this->id, 15);
        if (!$lock->get()) {
            return;
        }
        $result = \App\Models\Recharge::query()->find($this->id);
        if (!$result || 1 != $result['status']) {
            return;
        }
        $data = $this->handleParams($result);

        $pay_channel_id_arr = $data['pay_channel_id_arr'];
        $PayChannel = PayChannel::whereIn('id', $pay_channel_id_arr)->where('recharge_status', 1)->pluck('channel_type', 'id');

        $makeStatus = 'error';

        foreach ($PayChannel as $key => $channel_type) {
            if ($channel_type == 1) {
                // 汇付天下
                // $this->huiFu($data);
            }
            // if ($channel_type == 2) {
            //     // 快钱
            //     $this->Kq($data, $key);
            // }

            if (in_array($channel_type, [$PayChannel::ALIPAY_TYPE, PayChannel::AQFPAY_TYPE])) {
                // 支付宝付款
                $makeStatus = $this->makeChannelRequest($data, $key, $channel_type);
            }

            if ($makeStatus == 'success') {
                // 成功后跳出
                break;
            }
        }

        if ($makeStatus == 'error') {
            // 代付申请失败返回申请金额
            Log::channel('recharge')->info('申请失败退款 ' . $data['order_id']);
            $this->errorOrder('申请失败退款', $data, $channel = 'recharge');
        }

        $lock->release();

    }

    // 构建订单参数
    public function handleParams($params)
    {
        $pay_channel_id = User::where('agent_id', $params['user_id'])->value('pay_channel_id');
        $pay_channel_id_arr = explode(',', $pay_channel_id);
        return [
            'id' => $params['id'],
            'order_id' => $params['order_id'],
            'user_id' => $params['user_id'],
            'bank_id' => $params['bank_id'],
            'batch_amt' => $params['batch_amt'],
            'batch_rate_amt' => $params['batch_rate_amt'],
            'pay_channel_id_arr' => $pay_channel_id_arr,
        ];
    }

    public function makeChannelRequest($arr, $pay_channel_id, $channel_type)
    {
        $channel = 'recharge';

        try {
            Log::channel($channel)->info($arr['order_id'] . '---开始申请');
            // 实际提现金额
            $batch_amt = $arr['batch_amt'];
            $PayChannel = PayChannel::where('id', $pay_channel_id)->first(['avl_bal', 'fee_amt', 'dataJson']);
            // 多个通道可用余额
            $avl_bal = $PayChannel['avl_bal'];
            // 通道预留手续费
            $fee_amt = $PayChannel['fee_amt'];

            if (bcsub($avl_bal, $fee_amt, 2) < $batch_amt) {
                RechargeModel::where('id', $arr['id'])->update(['error_msg' => '通道金额不足', 'status' => 3]);
                throw new Exception('通道金额不足');
            }

            RechargeModel::where('id', $arr['id'])->update(['error_msg' => '已提交正在处理中！']);
            // 已提现充值金额
            $list_amount = RechargeList::where(['recharge_id' => $arr['id']])->sum('amount');
            // 剩余可提现金额
            $amount = bcsub($batch_amt, $list_amount, 2);
            if ($amount <= 0) {
                // 转账成功，直接结束循环
                throw new Exception('提现金额不能为0');
            }

            $recharge_list_order = generateOrderNumber();

            $config = json_decode($PayChannel['dataJson'], true);

            // // 节假日费率金额 0.5元
            $batch_rate_amt = isset($config['fee_amt']) ? $config['fee_amt'] : 0;
            // // 通道可用余额
            $avl_bal = bcsub($avl_bal, $batch_rate_amt, 2);
            Log::channel($channel)->info("通道：{$pay_channel_id}---开始下发申请：{$recharge_list_order}---金额：{$amount}");
            if ($avl_bal < $amount) {
                // 通道可用余额不足，就把通道余额清空
                throw new Exception('通道金额不足');
            }

            $UsersBank = UsersBank::where('id', $arr['bank_id'])->first();

            DB::beginTransaction();
            // 写入充值记录
            $RId = RechargeList::insertGetId([
                'agent_id' => $arr['user_id'],
                'bb_bank_id' => $arr['bank_id'],
                'recharge_id' => $arr['id'],
                'recharge_list_order' => $recharge_list_order,
                'amount' => $amount,
                'amount_san' => $amount,
                'pay_channel_id' => $pay_channel_id,
                'status' => 2,
                'qx_status' => 4,
                'pay_at' => date("Y-m-d H:i:s"),
                'created_at' => date("Y-m-d H:i:s"),
            ]);
            if ($RId) {
                DB::commit();
                $TransferPluginArr = [
                    'recharge_list_order' => $recharge_list_order,
                    'amount' => $amount,
                    'cardHolderName' => $UsersBank['name'],
                    // 'bankName' => '城市商业银行',
                    'pan' => $UsersBank['num'],
                    'pay_channel_id' => $pay_channel_id,
                    'config' => $config
                ];
                switch ($channel_type) {
                    case PayChannel::ALIPAY_TYPE:
                        // 支付宝原声
                        $data = (new AliPayController())->TransferPlugin($TransferPluginArr);
                        break;
                    case PayChannel::AQFPAY_TYPE:
                        // 安全付
                        $data = (new PaySecurityController())->TransferPlugin($TransferPluginArr);
                        break;
                    default:
                        // 通道异常
                        $data = ['status' => 400, 'message' => '通道不存在'];
                }

                if ($data['status'] == 200) {
                    PayChannel::where('id', $pay_channel_id)->decrement('avl_bal', ($amount + $fee_amt));
                    // 申请成功
                    RechargeList::where('id', $RId)->update(['error_msg' => $data['message']]);
                    if ($channel_type == PayChannel::ALIPAY_TYPE) {
                        RechargeModel::where('id', $arr['id'])->update(['error_msg' => '下发成功', 'status' => 2]);
                    } else {
                        RechargeModel::where('id', $arr['id'])->update(['error_msg' => '申请成功,等待回调']);
                    }
                    Log::channel($channel)->info("通道：{$pay_channel_id}---开始下发申请：{$recharge_list_order}---金额：{$amount}---申请成功");
                    Log::channel($channel)->info($arr['order_id'] . '---结束申请');
                    return 'success';
                } else {
                    // 申请失败
                    RechargeList::where('id', $RId)->update(['error_msg' => $data['message'], 'status' => 3, 'qx_status' => 3]);
                    Log::channel($channel)->info("通道：{$pay_channel_id}---开始下发申请：{$recharge_list_order}---金额：{$amount}---申请失败");
                    throw new Exception($data['message']);
                }
            }
            Log::channel($channel)->info($arr['order_id'] . '子订单创建失败---结束申请');
            throw new Exception('子订单创建失败');
        } catch (\Throwable $th) {
            // 代付申请失败返回申请金额
            Log::channel($channel)->info('申请失败 ' . $arr['order_id'] . $th->getMessage(), [$th->getLine(), $th->getFile()]);
            // $this->errorOrder($th->getMessage(), $arr, $channel);
            // 通道不通，更换通道
            return 'error';
        }
    }
    /**
     * 快钱代付
     * @param mixed $arr
     * @throws \Exception
     * @return void
     */
    public function Kq($arr, $pay_channel_id)
    {
        $channel = 'recharge';

        try {
            Log::channel($channel)->info($arr['order_id'] . '---开始申请');
            // 实际提现金额
            $batch_amt = $arr['batch_amt'];
            $PayChannel = PayChannel::where('id', $pay_channel_id)->first(['avl_bal', 'fee_amt', 'dataJson']);
            // 多个通道可用余额
            $avl_bal = $PayChannel['avl_bal'];
            // 通道预留手续费
            $fee_amt = $PayChannel['fee_amt'];

            if (bcsub($avl_bal, $fee_amt, 2) < $batch_amt) {
                RechargeModel::where('id', $arr['id'])->update(['error_msg' => '通道金额不足', 'status' => 3]);
                throw new Exception('通道金额不足');
            }

            RechargeModel::where('id', $arr['id'])->update(['error_msg' => '已提交正在处理中！']);
            // 已提现充值金额
            $list_amount = RechargeList::where(['recharge_id' => $arr['id']])->sum('amount');
            // 剩余可提现金额
            $amount = bcsub($batch_amt, $list_amount, 2);
            if ($amount <= 0) {
                // 转账成功，直接结束循环
                throw new Exception('提现金额不能为0');
            }

            $recharge_list_order = generateOrderNumber();

            $config = json_decode($PayChannel['dataJson'], true);

            // // 节假日费率金额 0.5元
            // $batch_rate_amt = isset($config['fee_amt']) ? $config['fee_amt'] : 0;
            // // 通道可用余额
            // $avl_bal = bcsub($avl_bal, $batch_rate_amt, 2);
            // Log::channel($channel)->info("通道：{$pay_channel_id}---开始下发申请：{$recharge_list_order}---金额：{$amount}");
            // if ($avl_bal < $amount) {
            //     // 通道可用余额不足，就把通道余额清空
            //     throw new Exception('通道金额不足');
            // }

            $UsersBank = UsersBank::where('id', $arr['bank_id'])->first();

            DB::beginTransaction();
            // 写入充值记录
            $RId = RechargeList::insertGetId([
                'agent_id' => $arr['user_id'],
                'bb_bank_id' => $arr['bank_id'],
                'recharge_id' => $arr['id'],
                'recharge_list_order' => $recharge_list_order,
                'amount' => $amount,
                'amount_san' => $amount,
                'pay_channel_id' => $pay_channel_id,
                'status' => 2,
                'qx_status' => 4,
                'pay_at' => date("Y-m-d H:i:s"),
                'created_at' => date("Y-m-d H:i:s"),
            ]);
            if ($RId) {
                DB::commit();
                $data = (new PayBankController())->C1019([
                    'externalRefNumber' => $recharge_list_order,
                    'amount' => $amount,
                    'cardHolderName' => $UsersBank['name'],
                    // 'bankName' => Kq::getKqBankIdMap($UsersBank['bank_type']),
                    'bankName' => '城市商业银行',
                    'pan' => $UsersBank['num'],
                    'pay_channel_id' => $pay_channel_id,
                ]);

                if ($data['resp_code'] == 200) {
                    PayChannel::where('id', $pay_channel_id)->decrement('avl_bal', ($amount + $fee_amt));
                    // 申请成功
                    RechargeList::where('id', $RId)->update(['error_msg' => $data['resp_desc']]);
                    RechargeModel::where('id', $arr['id'])->update(['error_msg' => '申请成功,等待回调']);
                    Log::channel($channel)->info("通道：{$pay_channel_id}---开始下发申请：{$recharge_list_order}---金额：{$amount}---申请成功");
                    Log::channel($channel)->info($arr['order_id'] . '---结束申请');
                    return;
                } else {
                    // 申请失败
                    RechargeList::where('id', $RId)->update(['error_msg' => $data['resp_desc'], 'status' => 3, 'qx_status' => 3]);
                    Log::channel($channel)->info("通道：{$pay_channel_id}---开始下发申请：{$recharge_list_order}---金额：{$amount}---申请失败");
                    throw new Exception($data['resp_desc']);
                }
            }
            Log::channel($channel)->info($arr['order_id'] . '子订单创建失败---结束申请');
            throw new Exception('子订单创建失败');
        } catch (\Throwable $th) {
            // 代付申请失败返回申请金额
            Log::channel($channel)->info('申请失败退款 ' . $arr['order_id'] . $th->getMessage(), [$th->getLine(), $th->getFile()]);
            return $this->errorOrder($th->getMessage(), $arr, $channel);
        }
    }

    // 汇付天下
    public function huiFu($arr)
    {
        $channel = 'recharge';
        try {
            Log::channel($channel)->info($arr['order_id'] . '---开始申请');

            $PayChannel = PayChannel::where(['channel_type' => 1, 'recharge_status' => 1])->whereIn('id', $arr['pay_channel_id_arr'])->get();
            // 支付通道未配置
            if (!$PayChannel) {
                throw new Exception('支付通道未配置');
            }
            // 实际提现金额
            $batch_amt = $arr['batch_amt'];
            // 多个通道可用余额
            $avl_bal = PayChannel::where(['channel_type' => 1, 'recharge_status' => 1])->whereIn('id', $arr['pay_channel_id_arr'])->sum('avl_bal');
            // 通道预留手续费
            $fee_amt = PayChannel::where(['channel_type' => 1, 'recharge_status' => 1])->whereIn('id', $arr['pay_channel_id_arr'])->sum('fee_amt');

            if (bcsub($avl_bal, $fee_amt, 2) < $batch_amt) {
                RechargeModel::where('id', $arr['id'])->update(['error_msg' => '通道金额不足', 'status' => 3]);
                throw new Exception('通道金额不足');
            }

            RechargeModel::where('id', $arr['id'])->update(['error_msg' => '已提交正在处理中！']);
            foreach ($PayChannel as $key => $value) {
                // 已提现充值金额
                $list_amount = RechargeList::where(['recharge_id' => $arr['id']])->sum('amount');
                // 剩余可提现金额
                $amount = bcsub($batch_amt, $list_amount, 2);
                if ($amount <= 0) {
                    // 转账成功，直接结束循环
                    break;
                }

                $recharge_list_order = generateOrderNumber();
                $config = json_decode($value['dataJson'], true);
                $pay_channel_id = $value['id'];
                // 节假日费率金额 0.5元
                $batch_rate_amt = isset($config['fee_amt']) ? $config['fee_amt'] : 0;
                // 通道可用余额
                $avl_bal = bcsub($value['avl_bal'], $batch_rate_amt, 2);
                Log::channel($channel)->info("通道：{$pay_channel_id}---开始下发申请：{$recharge_list_order}---金额：{$amount}");
                if ($avl_bal < $amount) {
                    // 通道可用余额不足，就把通道余额清空
                    $amount = $avl_bal;
                }

                $UserBbBank = UserBbBank::where([
                    'bank_id' => $arr['bank_id'],
                    'user_id' => $arr['user_id'],
                    'pay_channel_id' => $pay_channel_id,
                ])->first();
                // 查询该银行信息是否入驻成功
                if (!$UserBbBank) {
                    // 没有入驻成功
                    // 个人用户基本信息开户
                    $data = (new HuiFuController())->userBasicDataIndv($arr['bank_id'], $pay_channel_id);
                    $status = $data['status'];
                    // 更新用户银行信息状态是否满足三要素 第一步
                    if ($status == 2) {
                        $bb_bank_id = $data['bb_bank_id'];
                        // 用户业务入驻 第二步
                        (new HuiFuController())->userBusiOpen($bb_bank_id);
                        UsersBank::where(['id' => $arr['bank_id']])->update(['status' => 2]);
                    } else {
                        // 入驻失败
                        UsersBank::where(['id' => $arr['bank_id']])->update(['status' => 3]);
                        throw new Exception('姓名、身份证号 不匹配');
                    }

                    // 入驻成功
                    $UserBbBank = UserBbBank::where([
                        'bank_id' => $arr['bank_id'],
                        'user_id' => $arr['user_id'],
                        'pay_channel_id' => $pay_channel_id,
                    ])->first();
                    if ($UserBbBank['attestation'] == 2) {
                        UsersBank::where(['id' => $arr['bank_id']])->update(['attestation' => 2]);
                    }
                }
                $bb_bank_id = $UserBbBank['id'];
                $attestation = $UserBbBank['attestation'];
                // 绑定成功
                if ($attestation == 2) {
                    // 调用汇聚支付接口
                    $paymentTradePay = (new HuiFuController())->paymentTradePay($amount, $recharge_list_order, $bb_bank_id, $config);
                    Log::channel($channel)->info("通道：{$pay_channel_id}---开始下发申请：{$recharge_list_order}---返回信息", $paymentTradePay);

                    if ($paymentTradePay['resp_code'] == '********') {
                        //
                        Log::channel($channel)->info("通道：{$pay_channel_id}---开始下发申请：{$recharge_list_order}---金额：{$amount}---余额转账成功");
                        PayChannel::where(['id' => $pay_channel_id])->decrement('avl_bal', ($amount + $batch_rate_amt));
                        // 写入充值记录
                        $listId = RechargeList::insertGetId([
                            'agent_id' => $arr['user_id'],
                            'bb_bank_id' => $bb_bank_id,
                            'recharge_id' => $arr['id'],
                            'recharge_list_order' => $recharge_list_order,
                            'amount' => $amount,
                            'amount_san' => $amount,
                            'pay_channel_id' => $value['id'],
                            'status' => 2,
                            'qx_status' => 1,
                            'pay_at' => date("Y-m-d H:i:s"),
                            'created_at' => date("Y-m-d H:i:s"),
                            'error_msg' => $paymentTradePay['resp_desc']
                        ]);
                        UserBbBank::where(['id' => $bb_bank_id])->increment('amount', $amount);
                        // 取现
                        dispatch(new TradeSettlementJob($listId, $arr['user_id']))->onQueue('TradeSettlement');
                        continue;
                    }
                } else {
                    // 绑定失败
                    UsersBank::where(['id' => $arr['bank_id']])->update(['attestation' => 3]);
                    throw new Exception($UserBbBank['msg']);
                }
                Log::channel($channel)->info("通道：{$pay_channel_id}---开始下发申请：{$recharge_list_order}---金额：{$amount}---余额转账失败");
            }
            Log::channel($channel)->info($arr['order_id'] . '---结束申请');
            // 已提现成功金额
            $list_amount = RechargeList::where(['recharge_id' => $arr['id']])->sum('amount');
            if ($list_amount > 0) {
                // 提现成功
                $user_id = $arr['user_id'];
                // $success_amount = bcadd($list_amount, $arr['batch_rate_amt'], 2);
                $success_amount = bcadd(0, $arr['batch_rate_amt'], 2);
                User::where('agent_id', $user_id)->update([
                    'freeze_amount' => DB::raw("freeze_amount - {$success_amount}"),
                    // 'withdraw_amount' => DB::raw("withdraw_amount + {$list_amount}"),
                ]);
                if ($list_amount == $batch_amt) {
                    //
                    RechargeModel::where('id', $arr['id'])->update(['error_msg' => '下发成功', 'status' => 2]);
                }
            }

            // 剩余未提现金额
            $amount = bcsub($batch_amt, $list_amount, 2);
            if ($amount > 0) {
                throw new Exception('通道金额不足,部分退款');
            }

        } catch (\Throwable $th) {
            // 代付申请失败返回申请金额
            Log::channel($channel)->info('申请失败退款 ' . $arr['order_id'] . $th->getMessage(), [$th->getLine(), $th->getFile()]);
            return $this->errorOrder($th->getMessage(), $arr, $channel);
        }
    }

    /**
     * 公共退款方法
     * @param mixed $error_msg
     * @param mixed $arr
     * @param mixed $channel
     * @return void
     */
    public function errorOrder($error_msg, $arr, $channel = 'recharge')
    {
        DB::beginTransaction();
        try {
            // 实际提现金额
            $batch_amt = $arr['batch_amt'];
            // 冻结金额
            $freeze_amount = bcadd($batch_amt, $arr['batch_rate_amt'], 2);
            // 提现成功金额
            $list_amount = RechargeList::where(['recharge_id' => $arr['id']])->whereIn('status', [1, 2])->sum('amount');
            // 剩余提现金额
            $amount = bcsub($batch_amt, $list_amount, 2);
            if ($list_amount == 0) {
                // 失败返回申请金额
                RechargeModel::where('id', $arr['id'])->update(['status' => 3, 'error_msg' => $error_msg, 'pay_at' => now()]);
            }

            if ($list_amount != 0 && $amount != 0) {
                // 通道金额不足,部分退款,但不退手续费
                RechargeModel::where('id', $arr['id'])->update(['status' => 4, 'error_msg' => $error_msg, 'pay_at' => now()]);
                $arr['batch_rate_amt'] = 0;
            }
            // 退款金额
            $recharge_amount = bcadd($amount, $arr['batch_rate_amt'], 2);

            $user_id = $arr['user_id'];
            $recharge_amount_q = User::where('agent_id', $user_id)->value('recharge_amount');
            $recharge_amount_h = bcadd($recharge_amount, $recharge_amount_q, 2);
            User::where('agent_id', $user_id)->update([
                'recharge_amount' => DB::raw("recharge_amount + {$recharge_amount}"),
                'freeze_amount' => DB::raw("freeze_amount - {$freeze_amount}"),
                'withdraw_amount' => DB::raw("withdraw_amount + {$list_amount}"),
            ]);
            // 写入帐变记录
            UserUpAmount::AddUserAccountChangeRecord(
                3,
                $user_id,
                $amount,
                $arr['batch_rate_amt'],
                $arr['order_id'],
                $recharge_amount_q,
                $recharge_amount_h
            );
            Log::channel($channel)->info($arr['order_id'] . '---结束申请');
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            Log::channel($channel)->info('申请失败===回滚错误 ' . $arr['order_id'] . $e->getMessage());
        }
    }
}
