<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateRechargeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('recharge', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('batch_no')->nullable()->comment('订单号');
            $table->string('order_id')->nullable()->comment('平台订单号');
            $table->string('user_id')->nullable()->comment('商户号');
            $table->integer('bank_id')->nullable()->comment('提现卡ID');
            $table->decimal('batch_amt')->comment('付款总金额');
            $table->string('notify_url')->nullable()->comment('回调地址');
            $table->timestamp('pay_at')->nullable()->comment('转账时间');
            $table->longText('desc')->nullable()->comment('备注');
            $table->integer('status')->default('1')->nullable()->comment('1待处理 2提现成功 3失败 4部分成功');
            $table->timestamp('user_notify_at')->nullable()->comment('用户回调时间');
            $table->integer('user_notify_num')->default('0')->nullable()->comment('用户回调次数');
            $table->string('user_ip')->nullable()->comment('用户请求ip');
            $table->string('user_status')->nullable()->comment('用户响应  success 响应成功，其他的每隔 5分钟回调一次，共3次');
            $table->decimal('batch_rate_amt')->nullable()->comment('手续费');
            $table->longText('error_msg')->nullable()->comment('错误信息');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('recharge');
    }
}
