<?php

namespace App\Admin\Controllers;

use App\Models\RechargeRate;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class RechargeRateController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new RechargeRate(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('name');
            $grid->column('rate')->display(function ($value) {
                return $value . '%';
            })->help('%');
            $grid->column('amount_min');
            $grid->column('amount_max');
            $grid->column('amount');
            $grid->column('type')->using([0 => '对私', 1 => '对公'])->label(['success', 'red']);
            $grid->column('status')->switch()->help("←下架 上架→");
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
            });

            $grid->disableViewButton();
            $grid->disableEditButton();
            $grid->enableDialogCreate();
            // 显示快捷编辑按钮
            $grid->showQuickEditButton();
            // 设置弹窗宽高，默认值为 '700px', '670px'
            $grid->setDialogFormDimensions('40%', '60%');
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new RechargeRate(), function (Show $show) {
            $show->field('id');
            $show->field('name');
            $show->field('rate');
            $show->field('amount_min');
            $show->field('amount_max');
            $show->field('amount');
            $show->field('status');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new RechargeRate(), function (Form $form) {
            $form->display('id');
            $form->text('name')->width(4)->required();
            $form->decimal('rate')->help('例：13% 填 13 ')->default(1)->width(4);
            $form->number('amount_min')->attribute('min', 0)->attribute('max', 99999)->default(0)->help('如：为5则大于等于5');
            $form->number('amount_max')->attribute('min', 0)->attribute('max', 999999)->default(1)->help('如：为500则小于500不包含500');
            $form->number('amount');
            $form->radio('type')->default(0)->options([0 => '对私', 1 => '对公']);
            $form->switch('status')->help("←下架 上架→");

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
