<?php

namespace App\Admin\Controllers;

use Carbon\Carbon;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Admin;
use App\Models\Recharge;
use App\Models\AdminUser;
use App\Models\OrdersLog;
use App\Models\PayChannel;
use App\Models\RechargeRate;
use App\Admin\Actions\Grid\User\MoneyCharge;
use App\Admin\Actions\Grid\User\Relationship;
use App\Admin\Actions\Grid\User\MoneyChargeTwo;
use Dcat\Admin\Http\Controllers\AdminController;

class UserController extends AdminController
{
    protected $PayChannel;
    protected $recharge_rate;
    protected $type;

    public function __construct()
    {
        $this->PayChannel = PayChannel::pluck('name', 'id')->toArray();
        // $this->PayChannel = PayChannel::where('channel_type', 1)->pluck('name', 'id')->toArray();

        $arr = [];
        RechargeRate::select(['name', 'id', 'amount_min', 'amount_max'])->get()->map(function ($item) use (&$arr) {
            //

            $arr[$item->id] = $item->name . '：' . $item->amount_min . '~' . $item->amount_max;
        });
        $this->recharge_rate = $arr;

        if (substr(request()->path(), 0, 255) == config('admin.route.prefix') . '/users-usdt') {
            $this->type = 2;
        } elseif (substr(request()->path(), 0, 255) == config('admin.route.prefix') . '/users-rmb') {
            //
            $this->type = 1;
        } else {
            $this->type = request()->get('type', 1);
        }
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new User(), function (Grid $grid) {
            // 用户是否有某个角色
            if (Admin::user()->isRole('agent')) {
                $grid->model()->where('id', Admin::user()->user_id);
                // $grid->column('name');
                $grid->column('agent_id')->copyable();
                $grid->column('key')->copyable();
                // $grid->column('bank_address', 'usdt钱包地址')->copyable();
                $grid->column('recharge_amount', '余额')->display(function ($value) {
                    //
                    if ($value < $this->credit_amount) {
                        //
                        return '<span class="text-danger">' . $value . '</span>';
                    }
                    return $value;

                })->help('余额=充值+授信；红色：使用授信');
                $grid->column('credit_amount', '授信')->help('该金额不参与计算，只是提示做作用。');
                $grid->column('today_recharge_amount', '今日收款金额')->display(function () {
                    return OrdersLog::where('user_id', $this->agent_id)->whereDate('date', now())->sum('amount');
                });
                $grid->column('withdraw_amount', '已提现(元)');
                // $grid->column('withdraw_usdt');
                $grid->column('freeze_amount');
                $grid->column('trade_type', '支付类型')->explode(',');

                // $grid->column('recharge_usdt');
                // $grid->column('recharge_amount', '累计Rmb')->sortable();
                $grid->disableRowSelector();
                $grid->disableCreateButton();
                $grid->disableEditButton();
                // 禁用
                // $grid->disableActions();
                $grid->disableViewButton();
                $grid->disableDeleteButton();
                $grid->actions(function (Grid\Displayers\Actions $actions) {

                    $agent_id = $actions->row->agent_id;
                    $actions->append('<a style="cursor: pointer;" class="act-qXM97bDFghxJtnID" href="user/accountlog?user_id=' . $agent_id . '"><i title="" class="feather icon-edit-1 grid-action-icon"></i> 账变明细 &nbsp;</a>');
                });
                $grid->setActionClass(\Dcat\Admin\Grid\Displayers\Actions::class); // 列中操作直接显示 不要默认的三个点

                $grid->actions(function (Grid\Displayers\Actions $actions) {
                    // $actions->append(new RegPassword());
                });
            } else {
                $type = $this->type;
                $grid->model()->where('type', $type);
                $grid->model()->setConstraints([
                    'type' => $type,
                ]);

                $grid->column('id')->sortable();
                $grid->column('name')->width('100px');
                $grid->column('agent_id')->copyable()->width('100px');
                $grid->column('key')->copyable();
                if ($type == 1) {
                    //
                    $grid->column('freeze_amount')->sortable();
                    $grid->column('recharge_amount', '余额')->display(function ($value) {
                        //
                        if ($value < $this->credit_amount) {
                            //
                            return '<span class="text-danger">' . $value . '</span>';
                        }
                        return $value;

                    })->help('余额=充值+授信；红色：使用授信');
                    $grid->column('credit_amount', '授信')->help('该金额不参与计算，只是提示做作用。');
                    $grid->column('today_recharge_amount', '今日收款金额')->display(function () {
                        return OrdersLog::where('user_id', $this->agent_id)->whereDate('date', now())->sum('amount');
                    });
                    $grid->column('withdraw_amount', '已提现(元)');
                    $grid->column('yesterday_amount', '昨日提现金额')->display(function () {
                        return Recharge::where('user_id', $this->agent_id)->where('status', 2)->whereDate('created_at', Carbon::parse('-1 days'))->sum('batch_amt');
                    });
                    $grid->column('today_amount', '今日提现金额')->display(function () {
                        return Recharge::where('user_id', $this->agent_id)->where('status', 2)->whereDate('created_at', now())->sum('batch_amt');
                    });
                }
                $grid->column('trade_type', '支付类型')->explode(',');

                // $grid->column('HXB_status')->switch()->help('←关闭 开启→');
                $grid->column('status')->select(['下架', '正常', '禁用']);
                $grid->column('created_at')->sortable();
                $grid->filter(function (Grid\Filter $filter) {
                    // 展开过滤器
                    $filter->expand();
                    // 更改为 panel 布局
                    $filter->panel();
                    $filter->equal('id')->width(3);
                    $filter->equal('agent_id')->width(3);
                    $filter->equal('type_mark')->select([1 => '客户下发', 2 => '自己下发'])->width(3);
                });
                $grid->disableViewButton();
                $grid->actions(function (Grid\Displayers\Actions $actions) {
                    $agent_id = $actions->row->agent_id;
                    $actions->append('<a style="cursor: pointer;" class="act-qXM97bDFghxJtnID" href="user/accountlog?user_id=' . $agent_id . '"><i title="" class="feather icon-edit-1 grid-action-icon"></i> 账变明细 &nbsp;</a>');
                    $pay_channel_id = explode(',', $actions->row->pay_channel_id);
                    if (in_array(7, $pay_channel_id)) {
                        // 快钱余额划转，A商户划转到B商户
                        // $actions->append(Relationship::make());
                    }
                    // 用户充值
                    $actions->append(MoneyChargeTwo::make()->setKey($actions->row->id));
                    // 资金调整
                    $actions->append(MoneyCharge::make()->setKey($actions->row->id));
                });
                // 开启字段选择器功能
                $grid->showColumnSelector();
                // 设置默认隐藏字段
                $grid->hideColumns(['key', 'created_at']);
            }
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new User(), function (Show $show) {
            $show->field('id');
            $show->field('agent_id');
            $show->field('key');
            $show->field('withdraw_amount');
            $show->field('recharge_amount');
            $show->field('edit');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new User(), function (Form $form) {

            $type = request()->get('type', 1);

            $form->row(function ($form) {
                $form->html('<div class="col-md-12"><h4 style="color:#a93232">商户信息</h4><hr></div>');
                $form->width(3)->text('name');
                $form->width(3)->text('agent_id')->value($this->getNumString(7));
                $form->width(3)->text('key')->value($this->getRandomString(24));
                // $form->width(3)->text('reg_password', '代付密码')->default(123456)->help('默认密码：123456')->required();
                $form->width(3)->radio('status')->options(['下架', '正常', '禁用'])->default(1);
            });

            $form->row(function ($form) {
                $form->html('<div class="col-md-12"><h4 style="color:#a93232">收款设置</h4><hr></div>');
                $form->width(3)->rate('recharge_point')->default(0);
                $form->width(3)->number('ip_count', 'IP限制')->default(0)->help('0不限制,限制IP每天访问收银台次数');
                $form->width(3)->decimal('min_amount_o')->default(1)->required();
                $form->width(3)->decimal('max_amount_o')->default(99999)->required();
                $form->width(3)->multipleSelect('trade_type', '收款方式')
                    ->options(['A_NATIVE' => '支付宝', 'T_JSAPI' => '微信', 'KQ_QUICK' => '快钱支付'])
                    ->saving(function ($value) {
                        // 转化成json字符串保存到数据库
                        return implode(",", $value);
                    })->required();
            });

            $form->row(function ($form) {
                $form->html('<div class="col-md-12"><h4 style="color:#a93232">代付设置</h4><hr></div>');
                $form->width(3)->decimal('min_amount_r')->default(1)->required();
                $form->width(3)->decimal('max_amount_r')->default(99999)->required();
                $form->width(3)->decimal('max_reg_amount')->default(0)->required()->help('0不限制（元）');
                $form->width(3)->number('max_recharge_count')->min(0);

                $form->width(3)->multipleSelect('pay_channel_id', '支付通道')
                    ->options(PayChannel::pluck('name', 'id')->toArray())
                    ->saving(function ($value) {
                        // 转化成json字符串保存到数据库
                        return implode(",", $value);
                    })->required();
                $form->width(6)->multipleSelect('recharge_rate_id')
                    ->options($this->recharge_rate)
                    ->saving(function ($value) {
                        // 转化成json字符串保存到数据库
                        return implode(",", $value);
                    })->required();
            });

            $form->row(function ($form) {
                $form->html('<div class="col-md-12"><h4 style="color:#a93232">充值代付时间设置</h4><hr></div>');
                $form->model()->order_start_time = $form->model()->order_start_time ?? '00:00:00';
                $form->model()->order_end_time = $form->model()->order_end_time ?? '23:59:59';
                $form->model()->recharge_start_time = $form->model()->recharge_start_time ?? '00:00:00';
                $form->model()->recharge_end_time = $form->model()->recharge_end_time ?? '23:59:59';
                $form->width(6)->timeRange('order_start_time', 'order_end_time', '充值开始截止时间设置')->required();
                $form->width(6)->timeRange('recharge_start_time', 'recharge_end_time', '代付开始截止时间设置')->required();
            });

            $form->row(function ($form) use ($type) {
                $form->html('<div class="col-md-12"><h4 style="color:#a93232">商户资金</h4><hr></div>');
                if ($type == 1) {
                    //
                    $form->width(3)->decimal('withdraw_amount')->value(0)->disable();
                    $form->width(3)->decimal('recharge_amount', '累计充值(元)')->value(0)->disable();
                    $form->width(3)->decimal('freeze_amount')->value(0)->disable();
                } else {
                    //
                    $form->width(3)->decimal('withdraw_amount')->value(0)->disable();
                    $form->width(3)->decimal('freeze_amount')->value(0)->disable();
                    $form->width(3)->decimal('recharge_amount', '余额')->value(0)->disable();
                    $form->width(3)->decimal('recharge_usdt', '累计充值多少USDT')->value(0)->disable();
                }
            });

            $form->row(function ($form) {
                $form->html('<div class="col-md-12"><h4 style="color:#a93232">其他</h4><hr></div>');
                $form->width(6)->textarea('edit');
            });
            $form->disableViewCheck();
            $form->disableEditingCheck();
            $form->disableCreatingCheck();
            $form->disableResetButton();
            $form->disableHeader();
        })->saving(function (Form $form) {
            $recharge_start_time = $form->recharge_start_time;
            $recharge_end_time = $form->recharge_end_time;
            $order_start_time = $form->order_start_time;
            $order_end_time = $form->order_end_time;
            if (($recharge_start_time >= $recharge_end_time) || ($order_start_time >= $order_end_time)) {
                //
                // return $form->response()->error('时间范围不正确，截止时间不能小于等于开始时间~');
            }
        });
    }

    /**
     * 生成随机字符串
     *
     * @param [type] $len
     * @param [type] $chars
     * @return void
     */
    public function getRandomString($len, $chars = null)
    {
        if (is_null($chars)) {
            $chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        }
        mt_srand(10000000 * (float) microtime());
        for ($i = 0, $str = '', $lc = strlen($chars) - 1; $i < $len; $i++) {
            $str .= $chars[mt_rand(0, $lc)];
        }
        return $str;
    }

    /**
     * 生成随机数字
     *
     * @param [type] $len
     * @param [type] $chars
     * @return void
     */
    public function getNumString($len, $chars = null)
    {
        if (is_null($chars)) {
            $chars = "0123456789";
        }
        mt_srand(10000000 * (float) microtime());
        for ($i = 0, $str = '', $lc = strlen($chars) - 1; $i < $len; $i++) {
            $str .= $chars[mt_rand(0, $lc)];
        }

        $exists = User::where('agent_id', $str)->exists();
        if ($exists) {
            // 加入存在再次调用生成
            $this->getNumString($len);
        }
        return $str;
    }
}
