<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateOrdersLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('orders_log', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('user_id')->default('')->comment('商户号');
            $table->string('pay_channel_id')->default('')->comment('支付通道');
            $table->decimal('amount')->default('0.00')->comment('金额');
            $table->decimal('amount_san')->default('0.00')->comment('实收');
            $table->string('date')->nullable()->comment('时间');
            $table->integer('count')->default('0')->nullable()->comment('数量');
            $table->decimal('Issued_amount')->default('0')->nullable()->comment('下发金额');
            $table->integer('Issued_count')->default('0')->comment('下发订单数量');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('orders_log');
    }
}
