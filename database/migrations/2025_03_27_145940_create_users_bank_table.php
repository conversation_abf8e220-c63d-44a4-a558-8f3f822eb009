<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUsersBankTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users_bank', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('user_id')->nullable()->comment('商户id');
            $table->string('num')->nullable()->comment('银行卡号');
            $table->string('name')->nullable()->comment('姓名');
            $table->string('idcard')->nullable()->comment('身份证号');
            $table->string('mobile_no')->nullable()->comment('手机号');
            $table->string('cert_begin_date')->nullable()->comment('个人证件有效期开始日期');
            $table->string('msg')->nullable()->comment('信息');
            $table->integer('status')->default('1')->nullable()->comment('是否认证1未认证 2已认证 3认证失败');
            $table->string('bank_type')->nullable()->comment('银行编码');
            $table->integer('attestation')->default('1')->nullable()->comment('状态：1未绑定 2已绑定 3绑定失败');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users_bank');
    }
}
