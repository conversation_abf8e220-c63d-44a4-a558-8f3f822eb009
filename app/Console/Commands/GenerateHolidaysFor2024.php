<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Holiday;
use Carbon\Carbon;

class GenerateHolidaysFor2024 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'holidays:generate-2024';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '获取法定节假日并存储';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $allHolidays = [
            // '2024-01-01', // 元旦
            // '2024-02-10', // 春节
            // '2024-02-11',
            // '2024-02-12',
            // '2024-02-13',
            // '2024-02-14',
            // '2024-02-15',
            // '2024-02-16',
            // '2024-02-17', // 春节假期结束
            // '2024-04-05', // 清明节
            // '2024-05-01', // 劳动节
            // '2024-06-10', // 端午节
            // '2024-10-01', // 国庆节
            // '2024-10-02',
            // '2024-10-03',
            // '2024-10-04',
            // '2024-10-05',
            // '2024-10-06',
            // '2024-10-07', // 国庆假期结束
        ];

        $start = Carbon::create(2025, 1, 1);
        $end = Carbon::create(2025, 12, 31);

        for ($date = $start; $date->lte($end); $date->addDay()) {
            if ($date->isSaturday() || $date->isSunday()) {
                $allHolidays[] = $date->format('Y-m-d');
            }
        }

        // 去重
        $allHolidays = array_unique($allHolidays, SORT_REGULAR);

        foreach ($allHolidays as $holidayDate) {
            Holiday::updateOrCreate(
                ['date' => $holidayDate],
                ['name' => $this->getHolidayName($holidayDate)]
            );
        }

        $this->info('获取法定节假日并存储成功.');

        return 0;
    }

    private function getHolidayName($date)
    {
        $holidays = [
            '2024-01-01' => '元旦',
            '2024-02-10' => '春节',
            '2024-02-11' => '春节',
            '2024-02-12' => '春节',
            '2024-02-13' => '春节',
            '2024-02-14' => '春节',
            '2024-02-15' => '春节',
            '2024-02-16' => '春节',
            '2024-02-17' => '春节',
            '2024-04-04' => '清明节',
            '2024-04-05' => '清明节',
            '2024-04-06' => '清明节',
            '2024-05-01' => '劳动节',
            '2024-05-02' => '劳动节',
            '2024-05-03' => '劳动节',
            '2024-06-14' => '端午节',
            '2024-09-17' => '中秋节',
            '2024-10-01' => '国庆节',
            '2024-10-02' => '国庆节',
            '2024-10-03' => '国庆节',
            '2024-10-04' => '国庆节',
            '2024-10-05' => '国庆节',
            '2024-10-06' => '国庆节',
            '2024-10-07' => '国庆节',
        ];

        return $holidays[$date] ?? '周末';
    }
}