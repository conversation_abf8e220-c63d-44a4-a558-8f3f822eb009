<?php

namespace App\Http\Middleware;

use Closure;
use App\Models\User;
use App\Models\Setting;
use App\Models\Recharge;
use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Http\Middleware\TrimStrings as Middleware;

class PayMiddleware extends Middleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        try {
            $data = $request->input();
            // if (in_array($data['s'], ['/api/payment/recharge'])) {
            // if (in_array($data['s'], ['/api/addRecharge'])) {
            //
            Log::channel('api_log')->info('接口日志', ['data' => $data, 'ip' => $request->ip(), 'url' => $request->url()]);
            // }
            unset($data['s']);

            if (!isset($data['sign'])) {
                // 签名不存在
                return response()->json(['code' => 400, 'message' => "签名不存在"]);
            }
            $agent_id = $request->input('agent_id');
            $Signature = $request->input('sign');
            $userArr = User::where('agent_id', $agent_id)->first(['key', 'freeze_amount', 'recharge_amount', 'status', 'max_reg_amount']);
            if (!$userArr) {
                // 商户不存在
                return response()->json(['code' => 400, 'message' => "商户不存在"]);
            }

            if ($userArr['status'] != 1) {
                // 商户异常
                return response()->json(['code' => 400, 'message' => "商户异常"]);
            }

            // 商户异常
            if ($userArr['freeze_amount'] < 0 || $userArr['recharge_amount'] < 0) {
                //
                User::where('agent_id', $agent_id)->update(['status' => 2]);
                return response()->json(['code' => 400, 'message' => "商户异常"]);
            }

            // if ($userArr['max_reg_amount'] > 0) {
            //     //
            //     $batch_amt = Recharge::where('user_id', $agent_id)->whereDate('created_at', now())->sum('batch_amt');
            //     if ($userArr['max_reg_amount'] < $batch_amt) {
            //         // 商户已限额,请联系客服
            //         return response()->json(['code' => 400, 'message' => "商户已限额,请联系客服"]);
            //     }
            // }

            $data['key'] = $userArr['key'];

            // 商户IP白名单
            // $user_ip = $userArr['user_ip'];
            // if ($user_ip) {
            //     if (!in_array($request->ip(), explode('|', $user_ip))) {
            //         // 联系管理员设置IP白名单
            //         return response()->json(['code' => 400, 'message' => "IP不在白名单中"]);
            //     }
            // }

            unset($data['sign']);
            ksort($data);

            $str = '';
            foreach ($data as $k => $v) {
                $str .= $k . '=' . $v . '&';
            }
            $str = mb_substr($str, 0, -1);

            $signature = md5($str);

            // 进行密钥对比
            if ($signature != $Signature) {
                // 签名验证错误
                // return response()->json(['code' => 400, 'message' => "签名验证错误"]);
            }
        } catch (\Throwable $th) {
            //throw $th;
            return response()->json(['code' => 400, 'message' => "系统发生异常"]);
        }
        // 密钥匹配正确
        return $next($request);
    }
}
