<?php
/**
 * 手动取现
 */

namespace App\Admin\Actions\Show;

use App\Models\Order;
use Illuminate\Http\Request;
use Dcat\Admin\Actions\Action;
use App\Jobs\TradeSettlementJob;
use Dcat\Admin\Actions\Response;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\NotifyController;
use App\Models\RechargeList;

class TradeSettlement extends Action
{
    /**
     * @return string
     * 手动取现
     */
    protected $title = '<i class="fa fa-undo"></i> 手动取现';

    protected $modalId = 'show-TradeSettlement-user';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle()
    {
        $id = $this->getKey();
        try {
            //code...
            Log::channel('user_notify')->info('>>>>>>>>收款手动取现订单开始>>>>>>>>');
            $agent_id = RechargeList::where('id', $id)->whereIn('qx_status', [1, 3])->value('agent_id');
            if (!$agent_id) {
                Log::channel('user_notify')->info('<<<<<<<<收款手动取现订单结束<<<<<<<<');
                return $this->response()->error('手动取现失败.');
            }
            RechargeList::where('id', $id)->update(['qx_status' => 1, 'recharge_list_order' => generateOrderNumber()]);
            dispatch(new TradeSettlementJob($id, $agent_id))->onQueue('TradeSettlement');

            return $this->response()->success('手动取现成功.')->refresh();
        } catch (\Throwable $th) {
            //throw $th;
            // Log::channel('user_notify')->info($th->getMessage());
            Log::channel('user_notify')->info(
                '响应错误',
                [$th->getMessage()]
            );
            Log::channel('user_notify')->info('<<<<<<<<收款手动取现订单结束<<<<<<<<');
            return $this->response()->error('手动取现失败.');
        }
    }

    public function confirm()
    {
        return ['你确定要手动取现吗？'];
    }
}
