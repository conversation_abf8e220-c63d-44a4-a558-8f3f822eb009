<?php
/*
 * @Author: your name
 * @Date: 2021-03-09 20:48:06
 *
 * @LastEditTime: 2021-07-01 11:00:53
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \huoke\app\Http\Controllers\Api\BaseController.php
 */

namespace App\Http\Controllers;

// require_once dirname(__FILE__) . "/../BsPaySdk/request/V2TradePaymentJspayRequest.php";

use App\Models\Users;
use BsPaySdk\core\BsPay;
use App\Models\PayChannel;
use Illuminate\Http\Request;
use BsPaySdk\core\BsPayClient;
use App\Responses\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use App\Services\Huifu\V2TradePaymentJspayRequest;

class BaseController extends Controller
{

    /**
     * 定义全局常量
     *
     */
    const CODE_SUCCESS = 200;
    const CODE_ERROR = 400;

    /**
     * 全局json响应
     *
     * @param integer $code
     * @param string $message
     * @param array $result
     */
    public function responseJson($code = 200, $message = 'success', $result = [])
    {
        return response()->json([
            'code' => $code,
            'message' => $message,
            'result' => $result,
        ])->setEncodingOptions(JSON_UNESCAPED_UNICODE);
    }


    public function sendErrorMsg(\Throwable $e, string $des = '')
    {
        $result = [
            '环境' => app()->environment() . '_' . (app()->runningInConsole() ? 'cli' : 'fpm'),
            '时间' => date('Y-m-d H:i:s'),
            '文件' => $e->getFile(),
            '状态码' => $e->getCode(),
            '错误信息' => $e->getMessage(),
            '错误行号' => $e->getLine(),
            '简单描述' => $des,
        ];
        $str = '';
        foreach ($result as $k => $v) {
            $str .= "{$k}:{$v}\n";
        }
        Http::asJson()->post(
            'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=176a9c9d-e91a-49a8-a0d5-6159db2cd246',
            ['msgtype' => 'text', 'text' => ['content' => $str]]
        );
    }

}
