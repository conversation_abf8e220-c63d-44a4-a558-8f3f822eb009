<?php

namespace App\Admin\Controllers;

use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Admin;
use App\Models\OrdersLog;
use App\Models\RechargeFail;
use App\Admin\Actions\Grid\Excel\RechargeModal;
use Dcat\Admin\Http\Controllers\AdminController;

class RechargeFailController extends AdminController
{
    protected $status_arr;

    public function __construct()
    {
        $this->status_arr = [1 => '待处理', 2 => '申请成功', 3 => '失败'];
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {

        return Grid::make((new RechargeFail())->with('user')->with('recharge'), function (Grid $grid) {
            $status = request()->get('status', 1);
            // $grid->model()->where('status', $status);
            $grid->model()->orderByDesc('id');
            $grid->column('id')->sortable();
            $grid->column('batch_no');
            $grid->tools(function (Grid\Tools $tools) {
                $tools->append(new RechargeModal());
            });
            if (Admin::user()->isRole('agent1')) {

                $user_id = Admin::user()->user_id;
                $agent_id = User::where('id', $user_id)->value('agent_id');
                $agent_status = User::where('id', $user_id)->value('agent_status');
                if ($agent_status == 1) {
                    $grid->tools(function (Grid\Tools $tools) {
                        $tools->append(new RechargeModal());
                    });
                }
                $grid->model()->where('user_id', $agent_id);
                $grid->column('batch_amt');
                $grid->column('detail_data')->display(function () {
                    // 
                    return $this->num . '|' . $this->name . '|' . $this->idcard;
                });
                $grid->column('request_cout')->help('请求失败大于3次后认定失败');
                if ($status == 3) {
                    //
                    $grid->column('error_msg');
                }

                $grid->column('status')->using($this->status_arr)->dot([
                    1 => '#fccb16',
                    2 => 'success',
                    3 => '#ee2746',
                    4 => 'default'
                ]);
                $grid->column('desc')->limit(20);
                $grid->column('recharge.status', '订单下发状态')->using([1 => '未支付', 2 => '提现成功', 3 => '失败', 4 => '部分成功', 5 => '订单不存在'])->dot([
                    1 => '#fccb16',
                    2 => 'success',
                    3 => '#ee2746',
                    4 => 'default'
                ]);
                $grid->column('recharge.error_msg', '订单错误信息');
                $grid->column('created_at');
                $grid->filter(function (Grid\Filter $filter) {
                    // 展开过滤器
                    $filter->expand();
                    // 更改为 panel 布局
                    $filter->panel();

                    $filter->equal('batch_no')->width(3);
                    $filter->equal('num')->width(3);
                    $filter->equal('name')->width(3);
                    $filter->equal('idcard')->width(3);
                    $filter->equal('status')->select($this->status_arr)->width(3);
                    $filter->between('created_at')->datetime()->width(6);
                });
                $grid->header(function ($collection) use ($agent_id) {
                    $amount = User::availableAmt($agent_id);
                    $recharge_amount = User::where('agent_id', $agent_id)->value('recharge_amount');
                    $accounting_amount = bcsub($recharge_amount, $amount, 2);
                    return "<div class='filter-box shadow-0 card mb-0'>可下发共 $amount 元&nbsp;|&nbsp;预计明天可下发 $accounting_amount 元</div>";
                });
            } else {

                $grid->column('user_id')->setAttributes(['style' => 'color:#2d9e2bd6']);
                $grid->column('user.name', '商户名称');
                $grid->column('batch_amt');
                $grid->column('detail_data')->display(function () {
                    // 
                    return $this->num . '|' . $this->name . '|' . $this->idcard;
                });
                $grid->column('request_cout')->help('请求失败大于3次后认定失败');
                if ($status == 3) {
                    //
                    $grid->column('error_msg');
                }
                $grid->column('status')->using($this->status_arr)->dot([
                    1 => '#fccb16',
                    2 => 'success',
                    3 => '#ee2746',
                    4 => 'default'
                ]);
                $grid->column('desc')->limit(20);

                $grid->column('recharge.status', '订单下发状态')->using([1 => '未支付', 2 => '提现成功', 3 => '失败', 4 => '部分成功', 5 => '订单不存在'])->dot([
                    1 => '#fccb16',
                    2 => 'success',
                    3 => '#ee2746',
                    4 => 'default'
                ]);
                $grid->column('recharge.error_msg', '订单错误信息');
                $grid->column('created_at');
                $grid->filter(function (Grid\Filter $filter) {
                    // 展开过滤器
                    $filter->expand();
                    // 更改为 panel 布局
                    $filter->panel();

                    $filter->equal('batch_no')->width(3);
                    $filter->equal('num')->width(3);
                    $filter->equal('name')->width(3);
                    $filter->equal('idcard')->width(3);
                    $filter->equal('user_id')->select(User::getUserName())->width(3);
                    $filter->equal('status')->select($this->status_arr)->width(3);
                    $filter->between('created_at')->datetime()->width(6);
                });
            }


            $grid->tools(function (Grid\Tools $tools) {
                // excle 导入
                $status = request()->get('status', 1);
                if ($status == 3) {
                    //target=_blank
                    $tools->append('<a href="RechargeFail?status=1"><div class="btn btn-default" style="color:#336650"><i class="fa fa-fw fa-bullseye"></i> 待处理订单</div></a>&nbsp;');
                }
                if ($status == 1) {
                    //
                    $tools->append('<a href="RechargeFail?status=3"><div class="btn btn-default" style="color:#881734d6"><i class="fa fa-fw fa-bullseye"></i> 失败订单</div></a>&nbsp;');
                }
                // $tools->append(new RechargeModal());
            });

            // $grid->column('updated_at')->sortable();
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // 付款凭证下载
                // $actions->append(PayTransferGetProof::make()->setKey($actions->row->order_id));
            });

            // 禁用
            // $grid->disableActions();
            // 禁用
            $grid->disableBatchActions();
            // 禁用删除按钮
            $grid->disableDeleteButton();
            // 禁用编辑按钮
            $grid->disableEditButton();
            // 禁用详情按钮
            $grid->disableViewButton();
            $grid->disableCreateButton();

            // 开启字段选择器功能
            $grid->showColumnSelector();
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new RechargeFail(), function (Form $form) {
            $form->display('id');
            $form->text('num')->required();
            $form->text('name')->required();
            $form->text('idcard')->required();
            $form->text('batch_amt')->required();

            // 去除整个工具栏内容
            $form->disableHeader();
            $form->disableResetButton();
            $form->disableViewCheck();
            $form->disableEditingCheck();
            $form->disableCreatingCheck();

        });
    }
}
