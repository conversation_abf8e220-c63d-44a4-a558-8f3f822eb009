<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class UserAccountChangeRecord extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'user_account_change_record';

    public $type = [1 => '代发', 2 => '充值', 3 => '退款', 4 => '转账', 5 => '授信'];

    public $type_color = [1 => 'success', 2 => '#fccb16', 3 => '#ee2746', 4 => '#a9334d', 5 => '#e55ce8'];

    protected $fillable = ['user_id', 'amount', 'service_charge', 'order_id', 'money_q', 'money_h', 'deleted_at', 'created_at', 'updated_at'];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'agent_id')->withTrashed();
    }
}
