<?php
/*
 * @Author: your name
 * @Date: 2021-07-13 09:52:43
 * @LastEditTime: 2021-07-21 17:33:46
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \huoke\app\Admin\Actions\Imports\MemberImport.php
 */

namespace App\Admin\Actions\Imports;

use Dcat\Admin\Admin;
use App\Models\Merchant;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Database\Eloquent\Model;
use Maatwebsite\Excel\Concerns\ToModel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use App\Models\RecommendData as DataModel;
use Illuminate\Contracts\Queue\ShouldQueue;
use PhpOffice\PhpSpreadsheet\Helper\Sample;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithStartRow;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use Maatwe<PERSON>ite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;
use PhpOffice\PhpSpreadsheet\Worksheet\MemoryDrawing;

HeadingRowFormatter::default('none');

class MemberImport implements ToModel, WithStartRow, WithBatchInserts, WithChunkReading
{
    private $arr;

    public function __construct($arr)
    {
        $this->arr = $arr;
    }

    /**
     * @param array $row
     *
     * @return Model|Model[]|null
     */
    public function model(array $row)
    {
        // 吧excel中的图片赋值进来
        $row[7] = isset($this->arr[$row[1]]) ? $this->arr[$row[1]] : '';

        // return true;
        // 数据去重
        $user = Merchant::where([
            'merchantNo' => $row[1],
        ])->exists();
        if ($user) {
            // 存在返回 null
            return null;
        }

        // 数据库对应的字段
        return new Merchant([
            'name' => $row[0],
            'type_id' => 1,
            'strategy_id' => '',
            'status' => 0,
            'sort' => 0,
            'edit' => '批量插入',
            'merchantNo' => $row[1],
            'username' => $row[2],
            'bank_name' => $row[3],
            'bank_num' => $row[4],
            'address' => $row[5],
            'qrcode' => $row[7],
            'created_at' => now(),
            'order_at' => now(),
        ]);
    }

    /**
     * 从第几行开始处理数据 就是不处理标题
     * @return int
     */
    public function startRow(): int
    {
        return 2;
    }

    public function batchSize(): int
    {
        return 450;
    }

    public function chunkSize(): int
    {
        return 450;
    }
}
