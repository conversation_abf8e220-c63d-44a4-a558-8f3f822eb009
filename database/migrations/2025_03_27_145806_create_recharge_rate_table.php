<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateRechargeRateTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('recharge_rate', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->nullable()->comment('名称');
            $table->decimal('rate')->nullable()->comment('费率 1% 填写 1');
            $table->decimal('amount_min')->nullable()->comment('最小限额');
            $table->decimal('amount_max')->nullable()->comment('最大限额');
            $table->decimal('amount')->nullable()->comment('手续费');
            $table->integer('status')->default('0')->nullable()->comment('状态 0关闭 1开启');
            $table->integer('type')->default('0')->nullable()->comment('状态1对公 0对私');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('recharge_rate');
    }
}
