<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\TestController;
use App\Http\Controllers\HuiFuController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\NotifyController;
use App\Http\Controllers\kq\PayBankController;
use App\Http\Controllers\GatePay\GatePayController;
use App\Http\Controllers\kq\ReceiveNotifyController;
use App\Http\Controllers\AqPay\PaySecurityController;
use App\Http\Controllers\kq\FirstCertificationController;
use App\Http\Controllers\kq\SecondCertificationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Route::middleware('auth:api')->get('/user', function (Request $request) {
//     return $request->user();
// });

Route::group(['middleware' => 'cors'], function () {
    Route::group(['middleware' => 'pay'], function () {
        Route::post('addOrder', [OrderController::class, 'addOrder'])->name('创建收款订单');
        Route::post('orderQuery', [OrderController::class, 'orderQuery'])->name('获取订单支付信息');
        Route::post('tradePay', [OrderController::class, 'tradePay'])->name('下发订单');
        Route::post('checkBalance', [OrderController::class, 'checkBalance'])->name('查询余额');

    });

    Route::get('order/orderInfo', [OrderController::class, 'orderInfo'])->name('微信获取订单支付信息');
    Route::get('order/orderPay', [OrderController::class, 'orderPay'])->name('微信获取充值地址');

    Route::any('order/notice/{channel}', [NotifyController::class, 'orderNotice'])->name('收款通知');
    Route::any('recharge/notice/{channel}', [NotifyController::class, 'rechargeNotice'])->name('下发通知');
    Route::get('wx/order/error', [NotifyController::class, 'wxOrderErrorNotice'])->name('微信收款超时通知');

    //----------------------------------计划任务---------------------------------------
    Route::get('balanceKqTransfer', [NotifyController::class, 'balanceKqTransfer'])->name('快钱余额A划转到快钱B，每天凌晨执行一次');
    //-------------汇付天下------ 每分钟执行一次
    Route::any('getHfOrderQuery', [HuiFuController::class, 'tradeAcctPaymentScanpayQueryArr'])->name('获取汇付天下订单信息并更新');
    Route::any('refresh-balances', [HuiFuController::class, 'refreshBalances'])->name('更新汇付余额');

    //----------------------------------项目批量下发通知地址---------------------------------------
    Route::any('user', function () {
        return 'success';
    })->name('系统维护回调地址');

    //----------------------------------项目自己的页面---------------------------------------
    Route::post('view/addOrder', [TestController::class, 'viewAddOrder']);
    Route::post('cashAddOrder', [TestController::class, 'cashAddOrder'])->name('收银台下单(定制)，搭配 cash 页面使用');

    //----------------------------------快钱---------------------------------------
    Route::any('M0001', [FirstCertificationController::class, 'M0001'])->name('查询余额');
    Route::any('A2001', [FirstCertificationController::class, 'A2001'])->name('消费签约申请');
    Route::any('A2002', [FirstCertificationController::class, 'A2002'])->name('签约扣款认证');
    Route::any('A2003', [SecondCertificationController::class, 'A2003'])->name('快捷支付申请');
    Route::any('A2004', [SecondCertificationController::class, 'A2004'])->name('快捷支付');
    Route::any('getKqBankMap', [SecondCertificationController::class, 'getKqBankMap'])->name('获取银行列表');
    Route::any('getUserBankInfo', [SecondCertificationController::class, 'getUserBankInfo'])->name('获取用户绑定的银行卡信息');
    //----------------------------------安全付测试---------------------------------------
    Route::any('A0001', [PaySecurityController::class, 'TransferPlugin'])->name('收款');


    Route::get('qyapi', function (Request $request) {
        Http::asJson()->post(
            'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b9de68e0-fc3c-4007-822d-e63c88ad1c37',
            json_decode($request->content, true)
        );
    })->name('发送一个企业微信消息');

    Route::any('gate', [GatePayController::class, 'index'])->name('gate');


});

