<?php

/**
 * Order表定时回调
 */
namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Order;
use App\Models\Matching;
use App\Models\RechargeFail;
use Illuminate\Http\Request;
use App\Models\MatchingOrder;
use App\Models\OrdersUserBatch;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\Api\NotifyController;
use App\Http\Controllers\Api\MatchingOrderController;

class RechargeBatchKernel extends Command
{
    protected $signature = 'recharge:batch';
    protected $description = '代发批量订单';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle(Request $request)
    {
        while (true) {
            self::do($request);
        }
    }

    public static function do($request)
    {
        try {
            //code...
            $data = RechargeFail::where('status', 1)->where('request_cout', '<', 4)->orderBy('updated_at')->first();

            $channel = 'user_add_recharge';
            if ($data) {
                //
                Log::channel($channel)->info('批量-------------一键代发开始------------');

                // 合并新数据到请求对象
                request()->merge([
                    'agent_id' => $data['user_id'],
                    'batch_no' => $data['batch_no'],
                    'amount' => $data['batch_amt'],
                    'card_name' => $data['name'],
                    'cert_no' => $data['idcard'],
                    'bank_code' => $data['num'],
                    'notify_url' => $data['notify_url'],
                ]);

                $tradePay = (new OrderController())->tradePay(request());
                $bodydata = $tradePay->getData(true);
                Log::channel($channel)->info('批量-------------返回数据------------', $bodydata);

                $code = $bodydata['code'] ?? 400;
                $message = $bodydata['message'] ?? '申请失败';
                if ($code == 200) {
                    //
                    $data->status = 2;
                } else {
                    if ($data->request_cout > 2) {
                        $data->status = 3;
                    }
                }
                $data->request_cout = $data->request_cout + 1;
                $data->desc = $data->desc . '--' . $message;
                $data->save();

                Log::channel($channel)->info('批量-------------一键代发结束------------');
                sleep(3);
            } else {
                // 没有核实订单就睡眠10秒
                sleep(20);
            }
        } catch (\Throwable $th) {
            //throw $th;
            Log::channel($channel)->info($th->getMessage());
            sleep(3600);
        }
    }
}
