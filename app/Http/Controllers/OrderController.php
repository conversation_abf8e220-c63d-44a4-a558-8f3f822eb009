<?php
/*
 * @Author: your name
 * @Date: 2021-03-04 10:17:27
 * @LastEditTime: 2021-04-22 10:34:05
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \huoke\app\Http\Controllers\Api\AuthController.php
 */

namespace App\Http\Controllers;


use Exception;
use App\Models\Kq;
use App\Models\User;
use App\Models\Order;
use App\Models\Recharge;
use App\Jobs\RechargeJob;
use App\Models\UsersBank;
use App\Models\PayChannel;
use App\Models\RechargeRate;
use Illuminate\Http\Request;
use App\Services\UserUpAmount;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use App\Http\Controllers\BaseController;
use App\Http\Controllers\HuiFuController;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\alipay\AliPayController;

class OrderController extends BaseController
{
    /**
     * 收款
     * @param \Illuminate\Http\Request $request
     * @throws \Exception
     * @return \Illuminate\Http\JsonResponse
     */
    public function addOrder(Request $request)
    {
        $channel = 'user_add_order';
        //
        Log::channel($channel)->info('>>>>>>>>>>>>>>>>>>>>');
        Log::channel($channel)->info('开始');
        $validator = Validator::make($request->post(), [
            'amount' => 'required|numeric',
            'agent_id' => 'required',
            'batch_no' => 'required',
            'notify_url' => 'required|url',
            // 'return_url' => 'required',
            'tradeType' => 'required|regex:/^[^<>&;]*$/',
        ], [
            'agent_id.required' => 'agent_id 不能为空',
            'batch_no.required' => 'batch_no 不能为空',
            // 'goods_name.required' => 'goods_name 不能为空',
            'notify_url.required' => 'notify_url 不能为空',
            // 'return_url.required' => 'return_url 不能为空',
            'tradeType.required' => 'tradeType 不能为空',
            'tradeType.regex' => 'tradeType 内容中不允许包含特殊字符',
            'amount.required' => 'amount 不能为空',
            'amount.numeric' => 'amount 格式不正确',
            'notify_url.url' => '返回地址格式不正确',
        ]);

        if ($validator->fails() == true) {
            Log::channel($channel)->info($validator->errors()->first());
            Log::channel($channel)->info('失败');
            Log::channel($channel)->info('<<<<<<<<<<<<<<<<<<<<');
            return $this->responseJson(400, $validator->errors()->first());
        }
        // 商户号
        $agent_id = $request->post('agent_id');
        // 商户订单号
        $batch_no = $request->post('batch_no');

        $lock_key = 'lock_' . $agent_id;
        $is_lock = Redis::setnx($lock_key, 1); // 加锁

        Log::channel($channel)->info('加锁', [$lock_key, $batch_no]);
        if ($is_lock) { // 获取锁权限
            // 防止死锁
            // 生成随机的过期时间，范围在 60 到 300 秒之间（1 到 5 分钟）
            $expire = rand(60, 300);
            Redis::expire($lock_key, $expire);

            try {
                Log::channel($channel)->info('请求数据', $request->input());
                // 金额
                $amount = abs($request->post('amount'));

                // 订单标题
                $goods_name = $request->post('goods_name', '汇付原生');
                // 支付类型
                $tradeType = $request->post('tradeType');
                // 同步通知
                $notify_url = $request->post('notify_url');
                // 异步通知
                $return_url = $request->post('return_url');

                $User = User::where('agent_id', $agent_id)->first();
                if (!$User) {
                    throw new Exception('商户不存在');
                }

                if ($amount < $User['min_amount_o'] || $amount > $User['max_amount_o']) {
                    // 单笔提现金额限制
                    throw new Exception("单笔收款金额限制,最小金额为 {$User['min_amount_o']} 元,最大金额为 {$User['max_amount_o']} 元");
                }

                if (!empty($User['order_start_time']) && !empty($User['order_end_time'])) {
                    // 代付时间的限制，规定时间内才可进行代付
                    $order_start_time = strtotime(date('Y-m-d') . ' ' . $User['order_start_time']);
                    $order_end_time = strtotime(date('Y-m-d') . ' ' . $User['order_end_time']);
                    $time = strtotime(date('Y-m-d H:i:s'));

                    if (($order_start_time < $time) && ($time < $order_end_time)) {
                        //
                    } else {
                        throw new Exception(
                            '收款时间不正确，请在每天的 ' . $User['order_start_time'] . ' 到 ' . $User['order_end_time'] . ' 进行收款！',
                        );
                    }
                }

                $trade_type = explode(',', $User['trade_type']);
                if (!in_array($tradeType, $trade_type)) {
                    // 支付类型不正确
                    throw new Exception('支付类型不正确');
                }

                $exists = Order::where('batch_no', $batch_no)->where('user_id', $agent_id)->exists();
                if ($exists) {
                    // 此订单已存在
                    throw new Exception('此订单已存在');
                }

                // 获取当天某个IP所有的通道ID 进行过滤
                $ip = $request->ip();
                $OrderPayChannelIdArr = Order::where('created_at', 'like', date('Y-m-d') . '%')
                    ->where('user_ip', $ip)
                    ->pluck('pay_channel_id')->toArray();

                $pay_channel_id_arr = explode(',', $User['pay_channel_id']);
                // 找出两个数组中相同的值
                $commonValues = array_intersect($OrderPayChannelIdArr, $pay_channel_id_arr);
                // 从第二个数组中移除相同的值
                // $pay_channel_id_arr = array_diff($pay_channel_id_arr, $commonValues);

                if (empty($pay_channel_id_arr)) {
                    throw new Exception('请求频繁，请稍后再试');
                }

                $order_id = generateOrderNumber();
                $add_at = now();
                Log::channel($channel)->info('订单号', ['商户订单号' => $batch_no, '平台订单号' => $order_id]);

                if ($tradeType == 'A_NATIVE') {
                    // 支付宝
                    $data = (new PayController())->NativePay([
                        'agent_id' => $agent_id,
                        'order_id' => $order_id,
                        'goods_name' => $goods_name,
                        'tradeType' => $tradeType,
                        'amount' => $amount,
                        'pay_channel_id_arr' => $pay_channel_id_arr,
                        'code' => '',
                    ], $channel);
                    if ($data['code'] == 400) {
                        // 轮询下单
                        throw new Exception('下单失败,支付通道不存在或请求频繁，请稍后再试');
                    }
                    $pageURL = $data['pay_url'];
                    $pay_channel_id = $data['pay_channel_id'];
                } else if ($tradeType == 'T_JSAPI') {
                    // 微信扫码
                    $pageURL = 'http';
                    if ($_SERVER["REQUEST_SCHEME"] != "http") {
                        $pageURL .= "s";
                    }
                    $pageURL = rtrim(env('APP_URL'), '/') . '/wxpay/#/?order_no=' . $order_id;

                    $pay_channel_id = 0;
                } else if ($tradeType == 'KQ_QUICK') {
                    // 快钱支付
                    $pageURL = 'http';
                    if ($_SERVER["REQUEST_SCHEME"] != "http") {
                        $pageURL .= "s";
                    }
                    $pageURL = rtrim(env('KQ_URL'), '/') . '/99bill/#/?order_no=' . $order_id;

                    $pay_channel_id = PayChannel::whereIn('id', $pay_channel_id_arr)
                        ->where('channel_type', 2)
                        ->where('status', 1)
                        ->where('order_status', 1)
                        ->value('id');

                    if (!$pay_channel_id) {
                        throw new Exception('下单失败,支付通道不存在');
                    }

                } else {
                    //
                    throw new Exception('支付方式不对');
                }

                DB::beginTransaction();

                $Order = Order::insert([
                    'amount' => $amount,
                    'amount_san' => 0,
                    'user_id' => $agent_id,
                    'goods_name' => $goods_name,
                    'add_at' => $add_at,
                    'status' => 1,
                    'tradeType' => $tradeType,
                    'order_id' => $order_id,
                    'batch_no' => $batch_no,
                    'notify_url' => $notify_url,
                    'return_url' => $return_url,
                    'user_ip' => $ip,
                    'user_status' => 'error',
                    'created_at' => now(),
                    'updated_at' => now(),
                    'qr_code' => $pageURL,
                    'pay_channel_id' => $pay_channel_id,
                ]);
                if ($Order) {
                    // 插入上游返回的支付订单链接
                    DB::commit();
                    Log::channel($channel)->info('下单成功');
                    Log::channel($channel)->info('<<<<<<<<<<<<<<<<<<<<');
                    /// 释放锁
                    // Redis::del($lock_key);
                    $cash_register_url = '';
                    if ($tradeType == 'A_NATIVE') {
                        // 支付宝扫码
                        $cash_register_url = rtrim(env('APP_URL'), '/') . '/cash/?order_no=' . $order_id;
                    }

                    return $this->responseJson(200, "请求成功", [
                        'url' => $pageURL,
                        'order_id' => $order_id,
                        // 'cash_register' => $cash_register_url
                    ]);
                } else {
                    throw new Exception('请求失败');
                }
            } catch (\Throwable $th) {
                //throw $th;
                // 释放锁
                Redis::del($lock_key);
                DB::rollBack();
                Log::channel($channel)->info('失败', [$th->getMessage()]);
                Log::channel($channel)->info('<<<<<<<<<<<<<<<<<<<<');
                return $this->responseJson(400, $th->getMessage());
            }
        } else {
            // 防止死锁
            if (Redis::ttl($lock_key) == -1) {
                Redis::expire($lock_key, 3);
            }
            Log::channel($channel)->info('执行失败', [$lock_key, $batch_no]);
            return $this->responseJson(400, '异常请求，请稍后再试');
        }
    }


    /**
     * 微信获取充值地址
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function orderPay(Request $request)
    {
        try {
            //
            $channel = 'user_add_order';
            $order_id = $request->get('order_id', '');
            $code = $request->get('code', '');
            $Order = Order::where(['order_id' => $order_id, 'status' => 1, 'is_show' => 1])->first();
            if ($Order) {
                // 微信打开过
                Order::where(['order_id' => $order_id])->update(['is_show' => 2]);
                $pay_channel_id = User::where('agent_id', $Order['user_id'])->value('pay_channel_id');
                $pay_channel_id_arr = explode(',', $pay_channel_id);

                $data = (new HuiFuController())->payment([
                    'order_id' => $order_id,
                    'goods_name' => $Order['goods_name'],
                    'tradeType' => $Order['tradeType'],
                    'amount' => $Order['amount'],
                    'code' => $code,
                    'pay_channel_id_arr' => $pay_channel_id_arr,
                ], $channel);

                if ($data['code'] == 400) {
                    // 轮询下单
                    Log::channel($channel)->info('轮询下单失败');
                    // throw new Exception('支付失败，请重新扫码支付');
                    throw new Exception('订单不存在或状态不正确');
                }

                Order::where('order_id', $order_id)->update(['pay_channel_id' => $data['pay_channel_id']]);

                $config = json_decode($data['pay_url'], true);

                return $this->responseJson(200, "请求成功", $config);
            }
            throw new Exception('订单不存在或状态不正确');
        } catch (\Throwable $th) {
            //throw $th;
            return $this->responseJson(400, $th->getMessage());
        }
    }

    /**
     * 获取订单支付信息
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function orderInfo(Request $request)
    {
        //
        $order_id = $request->get('order_id', '');
        $data = Order::where(['order_id' => $order_id, 'status' => 1, 'is_show' => 1])->first();
        if ($data) {
            return $this->responseJson(200, "请求成功", [
                "amount" => $data['amount'],
                "appid" => "wxd1dd16c012c4b568",
                'agreement' => 'https://www.99bill.com/seashell/html/agreement/oneclickagreement.html'
            ]);
        }
        return $this->responseJson(400, "订单不存在或状态不正确");
    }

    /**
     * 获取订单支付信息
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function orderQuery(Request $request)
    {
        // 商户订单号
        $agent_id = $request->get('agent_id');
        $batch_no = $request->get('batch_no');

        $data = Order::where(['batch_no' => $batch_no, 'user_id' => $agent_id])->first([
            'batch_no',
            'order_id',
            'amount',
            'status',
            'pay_at'
        ]);
        if ($data) {
            return $this->responseJson(200, "请求成功", $data->toArray());
        }
        return $this->responseJson(400, "订单不存在");
    }

    /**
     * 下发
     * @param \Illuminate\Http\Request $request
     * @throws \Exception
     * @return \Illuminate\Http\JsonResponse
     */
    public function tradePay(Request $request)
    {
        $channel = 'user_add_recharge';
        //
        Log::channel($channel)->info('>>>>>>>>>>>>>>>>>>>>');
        Log::channel($channel)->info('开始');
        $validator = Validator::make($request->input(), [
            'amount' => 'required|numeric',
            'agent_id' => 'required',
            'batch_no' => 'required|regex:/^[^<>&;]*$/',
            'card_name' => 'required|regex:/^[^<>&;]*$/',
            'cert_no' => 'nullable|regex:/^[^<>&;]*$/',
            // 'bank_code' => 'required|regex:/^\d{16,19}$/|not_regex:/[^0-9]/',
            'bank_code' => 'required',
            // 'bank_type' => 'required|regex:/^[^<>&;]*$/',
            'notify_url' => 'required|url|regex:/^[^<>&;]*$/',
        ], [
            'agent_id.required' => 'agent_id 不能为空',
            'batch_no.required' => 'batch_no 不能为空',
            'notify_url.required' => 'notify_url 不能为空',
            'card_name.required' => 'card_name 不能为空',
            'cert_no.required' => 'cert_no 不能为空',
            'bank_type.required' => 'bank_type 不能为空',
            'bank_code.required' => 'bank_code 不能为空',
            'bank_code.regex' => 'bank_code 必须为16到19位数字',
            'amount.required' => 'amount 不能为空',
            'amount.numeric' => 'amount 格式不正确',
            'notify_url.url' => '回调地址格式不正确',
            'batch_no.regex' => 'batch_no 内容中不允许包含特殊字符',
            'notify_url.regex' => 'notify_url 内容中不允许包含特殊字符',
            'card_name.regex' => 'card_name 内容中不允许包含特殊字符',
            'cert_no.regex' => 'cert_no 内容中不允许包含特殊字符',
            'bank_type.regex' => 'bank_type 内容中不允许包含特殊字符',
        ]);

        if ($validator->fails() == true) {
            Log::channel($channel)->info($validator->errors()->first());
            Log::channel($channel)->info('失败');
            Log::channel($channel)->info('<<<<<<<<<<<<<<<<<<<<');
            return $this->responseJson(400, $validator->errors()->first());
        }
        // 商户号
        $agent_id = $request->input('agent_id');
        // 商户订单号
        $batch_no = $request->input('batch_no');
        // 银行类型
        // $bank_type = $request->input('bank_type', 'ABC');
        $bank_type = Kq::getRandomBankKey();

        $lock_key = 'lock_' . $agent_id;
        $is_lock = Redis::setnx($lock_key, 1); // 加锁

        Log::channel($channel)->info('加锁', [$lock_key, $batch_no]);
        if ($is_lock) { // 获取锁权限
            // 防止死锁
            Redis::expire($lock_key, 3);
            DB::beginTransaction();
            try {
                Log::channel($channel)->info('请求数据', $request->input());
                // 银行号
                $num = $request->input('bank_code');
                // 卡户名
                $name = $request->input('card_name');
                // 卡号
                $idcard = $request->input('cert_no');
                // 回调地址
                $notify_url = $request->input('notify_url');
                // 金额
                $amount = abs($request->input('amount'));
                // 平台订单号
                $order_id = generateOrderNumber();
                $User = User::where('agent_id', $agent_id)->first();
                if (!$User) {
                    throw new Exception('商户不存在');
                }
                if (!empty($User['recharge_start_time']) && !empty($User['recharge_end_time'])) {
                    // 代付时间的限制，规定时间内才可进行代付
                    $recharge_start_time = strtotime(date('Y-m-d') . ' ' . $User['recharge_start_time']);
                    $recharge_end_time = strtotime(date('Y-m-d') . ' ' . $User['recharge_end_time']);
                    $time = strtotime(date('Y-m-d H:i:s'));

                    if (($recharge_start_time < $time) && ($time < $recharge_end_time)) {
                        //
                    } else {
                        throw new Exception(
                            '代付时间不正确，请在每天的 ' . $User['recharge_start_time'] . ' 到 ' . $User['recharge_end_time'] . ' 进行代付！',
                        );
                    }
                }
                if ($User['max_reg_amount'] > 0) {
                    //
                    if ($User['max_reg_amount'] < Recharge::where('user_id', $agent_id)->whereDate('created_at', now())->sum('batch_amt')) {
                        // 商户已限额,请联系客服
                        throw new Exception("商户已限额,请联系客服");
                    }
                }

                $exists = Recharge::where('batch_no', $batch_no)->where('user_id', $agent_id)->exists();
                if ($exists) {
                    throw new Exception('此订单已存在');
                }

                if ($amount < $User['min_amount_r'] || $amount > $User['max_amount_r']) {
                    // 单笔提现金额限制
                    throw new Exception('单笔提现金额限制');
                }

                $recharge_rate_id = explode(',', $User['recharge_rate_id']);
                $RechargeRate = RechargeRate::select(['rate', 'amount'])
                    ->where('amount_min', '<=', $amount)
                    ->where('amount_max', '>=', $amount)
                    ->whereIn('id', $recharge_rate_id)
                    ->where('status', 1)->first();
                if (!$RechargeRate) {
                    throw new Exception('没有设置代付费率');
                }
                // 商户今日可用余额
                $Amt = User::availableAmt($agent_id);

                // 判断商户可用金额是否充足
                // 手续费
                $batch_rate_amt = bcadd($RechargeRate['amount'], bcmul($amount, bcdiv($RechargeRate['rate'], 100), 6), 2);
                if (
                    bccomp(
                        '0',
                        bcsub($Amt, bcadd($batch_rate_amt, $amount, 2), 2),
                        2
                    ) != -1
                ) {
                    throw new Exception("商户余额不足");
                }
                $UsersBank = UsersBank::firstOrCreate([
                    'user_id' => $agent_id,
                    'num' => $num,
                    'name' => $name,
                    'idcard' => $idcard,
                ], [
                    'user_id' => $agent_id,
                    'num' => $num,
                    'name' => $name,
                    'idcard' => $idcard,
                    'bank_type' => $bank_type,
                    'mobile_no' => generateRandomPhoneNumber(),
                    'cert_begin_date' => IdCardRandomDate($idcard),
                    'status' => 1,
                    'attestation' => 1
                ]);

                if (!$UsersBank) {
                    // throw new Exception("该银行卡信息不存在或未认证");
                }

                // 下发金额+手续费
                $tmpAmount = bcadd($amount, $batch_rate_amt, 2);
                User::where('agent_id', $agent_id)->update([
                    'recharge_amount' => DB::raw("recharge_amount - $tmpAmount"),
                    'freeze_amount' => DB::raw("freeze_amount + $tmpAmount"),
                ]);
                $recharge_amount_h = bcsub($User->recharge_amount, $tmpAmount, 2);
                $recharge_amount_q = $User->recharge_amount;

                // 写入帐变记录
                UserUpAmount::AddUserAccountChangeRecord(1, $agent_id, $amount, $batch_rate_amt, $order_id, $recharge_amount_q, $recharge_amount_h);
                $recharge_id = Recharge::query()->insertGetId([
                    'batch_no' => $batch_no,
                    'order_id' => $order_id,
                    'user_id' => $agent_id,
                    'bank_id' => $UsersBank['id'],
                    'batch_amt' => $amount,
                    'notify_url' => $notify_url,
                    'error_msg' => "待处理",
                    'user_status' => 'error',
                    'user_notify_num' => 0,
                    'batch_rate_amt' => $batch_rate_amt,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                if ($recharge_id) {
                    DB::commit();
                    /// 释放锁
                    Log::channel($channel)->info('下单成功');
                    Log::channel($channel)->info('<<<<<<<<<<<<<<<<<<<<');
                    /// 释放锁
                    Redis::del($lock_key);
                    dispatch(new RechargeJob($recharge_id, $agent_id))->onQueue('Recharge');
                    return $this->responseJson(200, '订单创建成功', ['order_id' => $order_id]);
                }
                throw new Exception("订单创建失败");
            } catch (\Throwable $th) {
                // 释放锁
                Redis::del($lock_key);
                DB::rollBack();
                Log::channel($channel)->info($th->getMessage(), [$th->getLine()]);
                Log::channel($channel)->info('失败');
                Log::channel($channel)->info('<<<<<<<<<<<<<<<<<<<<');

                return $this->responseJson(400, $th->getMessage());
            }
        } else {
            // 防止死锁
            if (Redis::ttl($lock_key) == -1) {
                Redis::expire($lock_key, 3);
            }
            Log::channel($channel)->info('执行失败', [$lock_key, $batch_no]);
            return $this->responseJson(400, '异常请求，请稍后再试');
        }
    }

    /**
     * 下游查询商户余额
     *
     * @param Request $request
     * @return
     */
    public function checkBalance(Request $request)
    {
        //
        $agent_id = $request->get('agent_id', '');
        $data = User::where('agent_id', $agent_id)->first(['recharge_amount', 'freeze_amount']);
        $available_amount = User::availableAmt($agent_id);
        $accounting_amount = bcsub($data['recharge_amount'], $available_amount, 2);
        $arr = [
            'recharge_amount' => $data['recharge_amount'],
            'freeze_amount' => $data['freeze_amount'],
            'available_amount' => $available_amount,
            'accounting_amount' => $accounting_amount,
        ];
        return $this->responseJson(200, '成功', $arr);
    }
}
