<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class User extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;


    /**
     * 查询出后台用户未绑定的商户
     *
     * @param [type] $query
     * @return void
     */
    public function scopeNotInUser($query)
    {
        return $query->whereNotIn('id', AdminUser::where('user_id', '<>', null)->pluck('user_id')->toArray());
    }

    /**
     * 编辑当前后台用户并排除当前数据不进行过滤
     *
     * @param [type] $query
     * @param [type] $id
     * @return void
     */
    public function scopeFilterIdNotInUser($query, $id)
    {
        return $query->whereNotIn('id', AdminUser::where('id', '<>', $id)->where('user_id', '<>', null)->pluck('user_id')->toArray());
    }

    /**
     * 获取用户当天可用余额
     * @param mixed $agent_id
     * @return string
     */
    static public function availableAmt($agent_id)
    {
        // 总余额
        $recharge_amount = self::where('agent_id', $agent_id)->value('recharge_amount');
        $pay_channel_id = self::where('agent_id', $agent_id)->value('pay_channel_id');
        $pay_channel_arr = explode(',', $pay_channel_id);
        $data = [
            date('Ymd')
        ];

        // 针对快钱，冻结金额次日11点到账，进行下发
        if (in_array(7, $pay_channel_arr) && date('His') < '110000') {
            $data[] = date('Ymd', strtotime('-1 day'));
        }

        // 当日冻结金额次日到账
        $amount = OrdersLog::where([
            'user_id' => $agent_id,
        ])->whereIn('date', $data)->sum('amount_san');

        // 返回可用余额 = 总余额-当日冻结金额
        return bcsub($recharge_amount, $amount, 2);
    }

    static public function getUserName()
    {
        // 总余额
        $data = self::get(['agent_id', 'name']);
        $arr = [];
        foreach ($data as $k => $v) {
            $arr[$v['agent_id']] = "{$v['agent_id']}--{$v['name']}";
        }
        return $arr;
    }

}
