<?php
/*
 * @Author: your name
 * @Date: 2021-04-01 17:06:30
 * @LastEditTime: 2021-05-21 14:43:25
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \huoke\app\Admin\Actions\Show\NoBankLeader.php
 */

namespace App\Admin\Actions\Show;

use App\Models\User;
use App\Models\Order;
use App\Models\Recharge;
use App\Models\PayChannel;
use App\Models\RechargeList;
use Illuminate\Http\Request;
use Dcat\Admin\Actions\Action;
use Dcat\Admin\Actions\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\NotifyController;

class RechargeError extends Action
{
    /**
     * @return string
     * 手动回调
     */
    protected $title = '<i class="fa fa-times" style="color: red;"> 手动失败</i> ';

    protected $modalId = 'show-RechargeError-user';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle()
    {
        // $arr = explode(',', $this->getKey());
        // $id = isset($arr[0]) ? $arr[0] : 0;
        // $recommend_id = isset($arr[1]) ? $arr[1] : 0;

        $id = $this->getKey();
        DB::beginTransaction();

        try {
            //code...

            Log::channel('user_notify')->info('>>>>>>>>下发手动处理下发失败开始>>>>>>>>');

            $Recharge = Recharge::query()->where('id', $id)->where('status', 1)->first();
            if ($Recharge) {
                $amount = $Recharge['batch_amt'];
                $user_id = $Recharge['user_id'];
                $RechargeList = RechargeList::where('recharge_id', $id)->get();
                if ($RechargeList) {
                    foreach ($RechargeList as $key => $value) {
                        // 
                        $value->status = 3;
                        $value->qx_status = 3;
                        $value->pay_at = now();
                        $value->error_msg = '手动处理失败';
                        $value->save();
                        PayChannel::where('id', $value->pay_channel_id)->increment('avl_bal', bcadd($value->amount, $Recharge['batch_rate_amt'], 2));
                    }
                }

                $Recharge->status = 3;
                $Recharge->error_msg = '手动处理失败';
                $Recharge->pay_at = now();
                $Recharge->save();

                (new NotifyController())->refundRecharge([
                    'order_id' => $Recharge['order_id'],
                    'user_id' => $user_id,
                    'recharge_amount' => $amount,
                    'batch_rate_amt' => $Recharge['batch_rate_amt'],
                ]);
                DB::commit();
                Log::channel('user_notify')->info('<<<<<<<<下发手动处理下发失败结束<<<<<<<<');
                return $this->response()->success('手动付款失败.')->refresh();
            }
            Log::channel('user_notify')->info('<<<<<<<<下发手动处理下发失败结束<<<<<<<<');
            return $this->response()->error('该订单不存在.');
        } catch (\Throwable $th) {
            DB::rollBack();
            Log::channel('user_notify')->info(
                '响应错误',
                [$th->getMessage()]
            );
            Log::channel('user_notify')->info('<<<<<<<<下发手动处理下发失败结束<<<<<<<<');
            return $this->response()->error('异常失败.');
        }
    }

    public function confirm()
    {
        return ['确定要处理失败吗？'];
    }
}
