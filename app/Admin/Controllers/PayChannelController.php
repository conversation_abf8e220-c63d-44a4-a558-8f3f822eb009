<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Admin;
use App\Models\PayChannel;
use Dcat\Admin\Layout\Content;
use App\Admin\Actions\Show\GetChannelAmount;
use App\Admin\Actions\Grid\Channel\MoneyCharge;
use Dcat\Admin\Http\Controllers\AdminController;

class PayChannelController extends AdminController
{
    protected $channel_type;
    protected $company;

    public function __construct()
    {
        $this->channel_type = (new PayChannel)->channel_type_code;
        $this->company = [];
    }

    public function index(Content $content)
    {
        if (substr(request()->path(), 0, 255) == config('admin.route.prefix') . '/payChannel-user-up') {
            if (Admin::user()->isRole('user_rmb')) {
                //
                return $content
                    ->header('线上充值')
                    ->body($this->grid());
            }

            // if (Admin::user()->isRole('user_usdt')) {
            //     //
            //     return $content
            //         ->header('线上充值')
            //         ->body($this->grid());
            // }

            return $content
                ->header('商户操作功能，管理员无需操作该功能！页面,无法展示！');
        }
        return $content
            ->header('支付通道')
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new PayChannel(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('name');
            $grid->column('channel_type')->using($this->channel_type);
            // $grid->column('dataJson');
            $grid->column('avl_bal', '可用余额');
            $grid->column('limit_time', 'N秒1单');

            $grid->column('status')->using([0 => '关闭', 1 => '上架'])->label([
                0 => '#ee2746',
                1 => 'success'
            ]);

            // $grid->column('edit');
            $grid->column('created_at')->sortable();
            $grid->column('updated_at', '最新更新时间')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                // 展开过滤器
                $filter->expand();
                // 更改为 panel 布局
                $filter->panel();
                $filter->equal('channel_type')->select($this->channel_type)->width(3);
            });

            $grid->disableViewButton();
            $grid->disableEditButton();
            $grid->enableDialogCreate();
            // 显示快捷编辑按钮
            $grid->showQuickEditButton();
            // 设置弹窗宽高，默认值为 '700px', '670px'
            $grid->setDialogFormDimensions('50%', '80%');

            $grid->tools('<div class="btn btn-primary" id="refresh-balances-btn"><i class="fa fa-reorder"></i> 刷新余额</div>&nbsp;');

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if (Admin::user()->inRoles(['administrator'])) {
                    $actions->append(MoneyCharge::make()->setKey($actions->row->id));
                    // 查看余额
                    $actions->append(GetChannelAmount::make()->setKey($actions->row->id));
                }
            });

            Admin::script(
                <<<JS
$(document).ready(function() {
    $('#refresh-balances-btn').click(function() {
        // 使用 AJAX 请求到服务器刷新余额
        $.ajax({
            url: '/api/refresh-balances',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                // 更新页面上的余额显示
                console.log(data);
                // 根据返回的数据更新每个通道的余额
                // 例如，假设 data 是一个包含 id 和 balance 的对象数组
                // 刷新页面
                location.reload();
            },
            error: function(error) {
                console.error('Error:', error);
            }
        });
    });
});
JS
            );

        });
    }
    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new PayChannel(), function (Show $show) {
            $show->field('id');
            $show->field('name');
            $show->field('status');
            $show->field('channel_type');
            $show->field('dataJson');
            $show->field('company_id');
            $show->field('edit');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new PayChannel(), function (Form $form) {
            $form->hidden('id');
            $form->text('name');
            $form->radio('channel_type')->options($this->channel_type)
                ->when(PayChannel::HUIFU_TYPE, function ($form) {
                    //
                    $dataJson = json_decode($form->model()->dataJson);
                    $form->html('<div class="col-md-12"><h4 style="color:#a93232">商户信息</h4><hr></div>');
                    $form->text('huifu_id', '商户号')->value($dataJson->huifu_id ?? '');
                    $form->text('product_id', '商户密钥')->value($dataJson->product_id ?? '');
                    $form->textarea('rsa_merch_private_key', '商户私钥')->value($dataJson->rsa_merch_private_key ?? '');
                    $form->textarea('rsa_huifu_public_key', '汇付公钥')->value($dataJson->rsa_huifu_public_key ?? '');
                    $form->text('fee_amt', '手续费')->value(0.5);
                    $form->rate('fee_rate', '节假日费率')->value($dataJson->fee_rate ?? 0.02);
                    $form->text('wx_appid', '微信appID')->value($dataJson->wx_appid ?? '');
                    $form->text('wx_secret', '微信密钥')->value($dataJson->wx_secret ?? '');
                })->when(PayChannel::KUAIQIAN_TYPE, function ($form) {
                    //
                    $dataJson = json_decode($form->model()->dataJson);
                    $form->html('<div class="col-md-12"><h4 style="color:#a93232">商户信息</h4><hr></div>');
                    $form->text('memberName', '付款商户名称	')->value($dataJson->account->memberName ?? '')->help('批量付款必填');
                    $form->text('memberCode', '快钱账户号')->value($dataJson->account->memberCode ?? '')->help('***********');
                    $form->text('merchantId', '商户号')->value($dataJson->account->merchantId ?? '')->help('***************');
                    $form->text('terminalId', '终端号')->value($dataJson->account->terminalId ?? '')->help('********');
                    $form->file('merchantCertPath', '商户私钥路径')->autoUpload()->help($dataJson->merchantCert->certPath ?? '未上传文件');
                    $form->text('merchantCertPwd', '商户私钥密码')->value($dataJson->merchantCert->certPassword ?? '');
                    $form->file('kuaiqianCertPath', '快钱公钥路径')->autoUpload()->help($dataJson->kuaiqianCert->certPath ?? '未上传文件');
                    $form->text('fee_amt', '手续费')->value(0.5);
                })->when(PayChannel::ALIPAY_TYPE, function ($form) {
                    //
                    $dataJson = json_decode($form->model()->dataJson);
                    $form->html('<div class="col-md-12"><h4 style="color:#a93232">商户信息</h4><hr></div>');
                    $form->text('alipay_user_id', '支付宝会员号')->value($dataJson->alipay_user_id ?? '')->help('开放平台获取账号ID');
                    $form->text('app_id', '商户号')->value($dataJson->app_id ?? '')->help('2017092208870819');
                    $form->file('ali_public_key', '支付宝公钥地址')->autoUpload()->help($dataJson->ali_public_key ?? '未上传文件：alipayCertPublicKey_RSA2.crt');
                    $form->textarea('private_key', '商户私钥密码')->value($dataJson->private_key ?? '');
                    $form->file('app_cert_public_key', '应用公钥证书路径')->autoUpload()->help($dataJson->app_cert_public_key ?? '未上传文件：appCertPublicKey_2017092208870819.crt');
                    $form->file('alipay_root_cert', '支付宝根证书路径')->autoUpload()->help($dataJson->alipay_root_cert ?? '未上传文件：alipayRootCert.crt');
                    $form->text('fee_amt', '手续费')->value(0.5);
                })->when(PayChannel::AQFPAY_TYPE, function ($form) {
                    //
                    $dataJson = json_decode($form->model()->dataJson);
                    $form->html('<div class="col-md-12"><h4 style="color:#a93232">商户信息</h4><hr></div>');
                    $form->text('accountId', '账号ID')->value($dataJson->accountId ?? '');
                    $form->text('payerShowName', '付款方显示名称')->value($dataJson->payerShowName ?? '');
                    $form->text('app_id', '应用ID')->value($dataJson->app_id ?? '');
                    $form->text('APP_SECRET', '应用密钥')->value($dataJson->APP_SECRET ?? '');
                    $form->text('fee_amt', '手续费')->value(0);
                    $form->text('pay_url', '支付接口')->value($dataJson->pay_url ?? '');

                })->default(1);
            $form->hidden('dataJson');
            $form->html('<div class="col-md-12"><hr></div>');
            $form->decimal('amount_limit', '收款金额限制')->required();
            $form->decimal('limit_time', 'N秒1单')->help('限制N秒内只能发起一笔收款,0表示不限制')->value(0)->required();
            $form->switch('status')->help('←下架 上架→');
            $form->radio('order_status', '是否支持收款')->options([0 => '否', 1 => '是'])->default(0);
            $form->radio('recharge_status', '是否支持下发')->options([0 => '否', 1 => '是'])->default(0);
            // $form->url('pay_channel_url')->required()->default(config('app.url'))->help('默认请求域名，请勿修改');
            $form->textarea('edit');

            $form->display('created_at');
            $form->display('updated_at');
        })->saving(function (Form $form) {
            $dataJson = [];
            $arr = [];

            if ($form->input('id')) {
                //
                $dataJson = json_decode($form->input('dataJson'), 1);
            }

            if (PayChannel::HUIFU_TYPE == $form->channel_type) {
                // 汇付天下
                $huifu_id = $form->input('huifu_id');
                $product_id = $form->input('product_id');
                $fee_amt = $form->input('fee_amt');
                $fee_rate = $form->input('fee_rate');
                $rsa_merch_private_key = $form->input('rsa_merch_private_key');
                if ($rsa_merch_private_key == '******') {
                    $rsa_merch_private_key = $dataJson['rsa_merch_private_key'];
                }
                $rsa_huifu_public_key = $form->input('rsa_huifu_public_key');
                if ($rsa_huifu_public_key == '******') {
                    $rsa_huifu_public_key = $dataJson['rsa_huifu_public_key'];
                }
                $wx_appid = $form->input('wx_appid');
                $wx_secret = $form->input('wx_secret');
                if ($wx_secret == '******') {
                    $wx_secret = $dataJson['wx_secret'];
                }
                $arr = [
                    'sys_id' => $huifu_id,
                    'huifu_id' => $huifu_id,
                    'product_id' => $product_id,
                    'rsa_merch_private_key' => $rsa_merch_private_key,
                    'rsa_huifu_public_key' => $rsa_huifu_public_key,
                    'wx_appid' => $wx_appid,
                    'wx_secret' => $wx_secret,
                    'fee_amt' => $fee_amt,
                    'fee_rate' => $fee_rate,
                ];
            }

            if (PayChannel::KUAIQIAN_TYPE == $form->channel_type) {

                $memberName = $form->input('memberName');
                $memberCode = $form->input('memberCode');
                $merchantId = $form->input('merchantId');
                $terminalId = $form->input('terminalId');
                $merchantCertPath = $form->input('merchantCertPath') ? storage_path('app/public/' . $form->input('merchantCertPath')) : $dataJson['merchantCert']['certPath'];
                $merchantCertPwd = $form->input('merchantCertPwd');
                $kuaiqianCertPath = $form->input('kuaiqianCertPath') ? storage_path('app/public/' . $form->input('kuaiqianCertPath')) : $dataJson['kuaiqianCert']['certPath'];

                $arr = [
                    "account" => [
                        "memberName" => $memberName,
                        "memberCode" => $memberCode,
                        "merchantId" => $merchantId,
                        "terminalId" => $terminalId,
                    ],
                    "merchantCert" => [
                        "certPath" => $merchantCertPath,
                        "certPassword" => $merchantCertPwd,
                    ],
                    "kuaiqianCert" => [
                        "certPath" => $kuaiqianCertPath
                    ]
                ];
            }

            if (PayChannel::ALIPAY_TYPE == $form->channel_type) {
                $app_id = $form->input('app_id');
                $alipay_user_id = $form->input('alipay_user_id');
                $private_key = $form->input('private_key');
                $ali_public_key = $form->input('ali_public_key') ? $form->input('ali_public_key') : $dataJson['ali_public_key'];
                $app_cert_public_key = $form->input('app_cert_public_key') ? $form->input('app_cert_public_key') : $dataJson['app_cert_public_key'];
                $alipay_root_cert = $form->input('alipay_root_cert') ? $form->input('alipay_root_cert') : $dataJson['alipay_root_cert'];

                $arr = [
                    "app_id" => $app_id,
                    "alipay_user_id" => $alipay_user_id,
                    "private_key" => $private_key,
                    "ali_public_key" => $ali_public_key,
                    "app_cert_public_key" => $app_cert_public_key,
                    "alipay_root_cert" => $alipay_root_cert,
                ];
            }

            if (PayChannel::AQFPAY_TYPE == $form->channel_type) {
                $accountId = $form->input('accountId');
                $payerShowName = $form->input('payerShowName');
                $app_id = $form->input('app_id');
                $APP_SECRET = $form->input('APP_SECRET');
                $fee_amt = $form->input('fee_amt');
                $pay_url = $form->input('pay_url');

                $arr = [
                    "app_id" => $app_id,
                    "accountId" => $accountId,
                    "payerShowName" => $payerShowName,
                    "APP_SECRET" => $APP_SECRET,
                    "fee_amt" => $fee_amt,
                    "pay_url" => $pay_url,
                ];
            }

            $form->deleteInput('fee_rate');
            $form->deleteInput('huifu_id');
            $form->deleteInput('product_id');
            $form->deleteInput('rsa_merch_private_key');
            $form->deleteInput('rsa_huifu_public_key');
            $form->deleteInput('wx_appid');
            $form->deleteInput('wx_secret');
            $form->deleteInput('memberName');
            $form->deleteInput('memberCode');
            $form->deleteInput('merchantId');
            $form->deleteInput('terminalId');
            $form->deleteInput('merchantCertPath');
            $form->deleteInput('merchantCertPwd');
            $form->deleteInput('kuaiqianCertPath');
            $form->deleteInput('alipay_user_id');
            $form->deleteInput('app_id');
            $form->deleteInput('private_key');
            $form->deleteInput('ali_public_key');
            $form->deleteInput('app_cert_public_key');
            $form->deleteInput('alipay_root_cert');
            $form->deleteInput('accountId');
            $form->deleteInput('payerShowName');
            $form->deleteInput('APP_SECRET');
            $form->deleteInput('pay_url');

            $form->dataJson = json_encode($arr, JSON_UNESCAPED_UNICODE);
        });
    }
}
