<?php
/*
 * @Author: your name
 * @Date: 2021-03-04 17:37:03
 * @LastEditTime: 2021-03-09 21:57:38
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \huoke\app\Admin\Controllers\OrderLogController.php
 */

namespace App\Admin\Controllers;

use Dcat\Admin\Grid;
use Dcat\Admin\Admin;
use App\Models\PayChannel;
use Dcat\Admin\Layout\Content;
use App\Admin\Repositories\PayChannelCount;
use Dcat\Admin\Http\Controllers\AdminController;

class PayChannelCountController extends AdminController
{
    protected $prefix;

    public function __construct()
    {
        $this->prefix = config('admin.route.prefix');
    }

    public function index(Content $content)
    {
        $grid = '';
        $path = request()->path();

        if ($path == "{$this->prefix}/channel/count") {
            $grid = 'channelOrder';
        }

        return $content
            ->header('统计')
            ->body($this->$grid());
    }

    /**
     * 供应商维度统计
     * @return Grid
     */
    protected function channelOrder()
    {
        $pay_channel_id = explode(',', Admin::user()->pay_channel_id);

        return Grid::make(new PayChannelCount($pay_channel_id), function (Grid $grid) use ($pay_channel_id) {
            $grid->column('pay_channel_id', '通道名称')->using(PayChannel::pluck('name', 'id')->toArray());
            $grid->column('date', '日期');
            $grid->column('total_amount', '收款金额');

            // $grid->column('total_count', '总计');
            // 禁用
            $grid->disableBatchActions();
            // 禁用
            $grid->disableCreateButton();
            $grid->disableActions();
            $grid->filter(function ($filter) use ($pay_channel_id) {
                // 展开过滤器
                $filter->expand();
                // 更改为 panel 布局
                $filter->panel();
                // 设置id字段的范围查询
                $filter->between('time', '查询时间')->datetime()->default(['start' => date('Y-m-01 00:00:01'), 'end' => date('Y-m-d 23:59:59')])->width(3);
                if (Admin::user()->isRole('administrator')) {
                    $filter->equal('pay_channel_id', '通道')->select(PayChannel::pluck('name', 'id'))->width(3);
                } else {
                    $filter->equal('pay_channel_id', '通道')->select(PayChannel::whereIn('id', explode(',', Admin::user()->pay_channel_id))->pluck('name', 'id'))->width(3);
                }
            });

            // 开启字段选择器功能
            $grid->showColumnSelector();
            // 设置默认隐藏字段

        })->title('通道维度统计');
    }
}
