<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreatePayChannelTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pay_channel', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->nullable()->comment('名称');
            $table->integer('status')->default('0')->nullable()->comment('0关闭 1开启');
            $table->integer('channel_type')->nullable()->comment('通道类型');
            $table->longText('dataJson')->nullable()->comment('参数');
            $table->integer('company_id')->nullable()->comment('公司账户');
            $table->string('edit')->nullable()->comment('备注');
            $table->integer('order_status')->default('0')->nullable()->comment('是否收款 0关闭 1开启');
            $table->integer('recharge_status')->default('0')->nullable()->comment('是否代发 0关闭 1开启');
            $table->decimal('recharge_point')->default('0.00')->nullable()->comment('充值点位%');
            $table->string('pay_channel_url')->nullable()->comment('分布式下单地址');
            $table->string('pub_pfx')->nullable();
            $table->decimal('amount_limit')->default('0.00')->nullable()->comment('收款限制金额');
            $table->decimal('balance_amt')->default('0.00')->nullable()->comment('账户余额');
            $table->decimal('avl_bal')->default('0.00')->nullable()->comment('可用余额');
            $table->decimal('fee_amt')->default('0.00')->nullable()->comment('手续费');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pay_channel');
    }
}
