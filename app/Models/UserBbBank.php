<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;

class UserBbBank extends Model
{
    use HasDateTimeFormatter;
    protected $table = 'user_bb_bank';

    protected $fillable = [
        'user_id',
        'bank_id',
        'pay_channel_id',
        'num',
        'name',
        'idcard',
        'huifu_id',
        'attestation',
        'status',
        'msg',
        'token_no',
        'cert_begin_date',
        'bank_type'
    ];
}
