<?php
/*
 * @Author: your name
 * @Date: 2021-03-04 14:16:23
 * @LastEditTime: 2021-07-07 10:58:27
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \huoke\app\Admin\Actions\Forms\Order\Assistant.php
 */

namespace App\Admin\Actions\Forms\Channel;

use Dcat\Admin\Admin;
use App\Models\Message;
use App\Models\PayChannel;
use Dcat\Admin\Widgets\Form;
use App\Models\User;
use App\Models\PayChannelOrder;
use Dcat\Admin\Traits\LazyWidget;
use Illuminate\Support\Facades\DB;
use App\Models\UsersRechargeAmount;
use Illuminate\Support\Facades\Log;
use Dcat\Admin\Contracts\LazyRenderable;

class MoneyCharge extends Form implements LazyRenderable
{
    use LazyWidget;

    /**
     * 处理请求
     *
     * @param array $input
     * @return void
     */
    public function handle(array $input)
    {
        DB::beginTransaction();
        try {
            //code...
            $id = $input['id'] ?? null;
            $money = $input['money'] ?? 0;

            PayChannel::where('id', $id)->increment('avl_bal', $money);

            DB::commit();

            return $this->response()->success('充值成功')->refresh();
        } catch (\Throwable $th) {
            //throw $th;

            DB::rollBack();

            return $this->response()->error('充值失败');
        }
    }

    /**
     * 创建表单
     *
     * @return void
     */
    public function form()
    {
        $this->decimal('money', '充值余额金额')->help('请不要轻易去修改: 新增or减少金额');
        // 设置隐藏表单，传递用户id
        $this->hidden('id');
    }
}
