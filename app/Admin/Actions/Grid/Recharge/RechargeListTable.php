<?php
/*
 * @Author: your name
 * @Date: 2021-03-04 14:16:15
 * @LastEditTime: 2021-03-09 19:16:32
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \huoke\app\Admin\Actions\Grid\Order\Assistant.php
 */

namespace App\Admin\Actions\Grid\Recharge;

use Dcat\Admin\Grid;
use Dcat\Admin\Admin;
use App\Models\RechargeList;
use Dcat\Admin\Grid\LazyRenderable;
use App\Admin\Actions\Show\TradeSettlement;

class RechargeListTable extends LazyRenderable
{
    public function grid(): Grid
    {
        return Grid::make(new RechargeList(), function (Grid $grid) {
            $id = $this->key;
            $grid->model()->where('recharge_id', $id);

            $grid->column('id')->sortable();
            if (Admin::user()->isRole('administrator')) {
                //
                $grid->column('pay_channel_id', '通道ID')->using(RechargeList::getPayChannelMap());
                $grid->column('bb_bank_id', '提现卡ID');
            }

            $grid->column('recharge_list_order', '子单号');
            $grid->column('amount', '金额');
            $grid->column('status', '余额转账')->using(RechargeList::getStatusMap())->label([
                1 => '#fccb16',
                2 => 'success',
                3 => '#ee2746',
            ]);
            $grid->column('qx_status', '用户取现')->using(RechargeList::getQxStatusMap())->label([
                1 => '#fccb16',
                2 => 'success',
                3 => '#ee2746',
                4 => 'default'
            ]);
            $grid->column('pay_at', '转账时间');
            $grid->column('error_msg', '错误信息');
            $grid->column('updated_at');

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $qx_status = $actions->row->qx_status;
                if (in_array($qx_status, [1, 3])) {
                    $actions->append(TradeSettlement::make()->setKey($actions->row->id));
                }
            });
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableEditButton();
            $grid->disableCreateButton();
            // $grid->quickSearch(['title', 'code']);
            // $grid->paginate(10);
            // $grid->disableActions();
        });
    }
}
