<?php

namespace App\Admin\Controllers;

use Carbon\Carbon;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use App\Models\Order;
use Dcat\Admin\Admin;
use App\Models\OrdersLog;
use App\Models\PayChannel;
use Dcat\Admin\Http\Controllers\AdminController;

class OrdersLogController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        if (Admin::user()->isRole('agent')) {
            //
            return $this->gridAgent();
        }

        return Grid::make(new OrdersLog(), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('user_id')->using(User::getUserName());
            $grid->column('pay_channel_id')->using(PayChannel::pluck('name', 'id')->toArray());
            $grid->column('amount')->setAttributes(['style' => 'color:#ce301fd6']);
            $grid->column('amount_san')->setAttributes(['style' => 'color:#2d9e2bd6']);
            $grid->column('yingli', '盈利')->display(function () {
                // 
                return bcsub($this->amount, $this->amount_san, 2);
            })->setAttributes(['style' => 'color:#e12ee6d6']);

            $grid->column('count', '订单数量')->setAttributes(['style' => 'color:#0a21b4d6']);
            $grid->column('cglv', '成功率')->display(function () {
                // 
                try {
                    //code...

                    $user_id = $this->user_id;
                    $pay_channel_id = $this->pay_channel_id;
                    $created_at = $this->created_at;
                    $date = Carbon::parse($created_at)->format('Y-m-d');

                    $all = Order::where('user_id', $user_id)->where('pay_channel_id', $pay_channel_id)->where('created_at', 'like', $date . '%')->count();

                    return bcdiv($this->count, $all, 4) * 100 . '%';
                } catch (\Throwable $th) {
                    //throw $th;
                    return '异常';
                }
            })->setAttributes(['style' => 'color:#2f8131']);
            $grid->column('date');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                // 展开过滤器
                $filter->expand();
                // 更改为 panel 布局
                $filter->panel();

                $filter->between('created_at')->date()->width(6);
                $filter->equal('user_id')->select(User::getUserName())->width(3);
                $filter->equal('pay_channel_id')->select(PayChannel::pluck('name', 'id')->toArray())->width(3);
            });
        });
    }
    protected function gridAgent()
    {
        return Grid::make(new OrdersLog(), function (Grid $grid) {
            $user_id = User::where('id', Admin::user()->user_id)->value('agent_id');
            $grid->model()->where('user_id', $user_id)->orderBy('id', 'desc');

            // $grid->column('id')->sortable();
            $grid->column('user_id')->using(User::getUserName());
            // $grid->column('pay_channel_id')->using(PayChannel::pluck('name', 'id')->toArray());
            $grid->column('amount')->setAttributes(['style' => 'color:#ce301fd6']);
            $grid->column('amount_san')->setAttributes(['style' => 'color:#2d9e2bd6']);
            $grid->column('count', '订单数量')->setAttributes(['style' => 'color:#0a21b4d6']);
            $grid->column('date');
            $grid->column('created_at');

            $grid->filter(function (Grid\Filter $filter) {
                // 展开过滤器
                $filter->expand();
                // 更改为 panel 布局
                $filter->panel();
                $filter->between('created_at')->date()->width(6);
            });

            $grid->disableActions();
            $grid->disableBatchActions();
            $grid->disableCreateButton();
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new OrdersLog(), function (Show $show) {
            $show->field('id');
            $show->field('user_id');
            $show->field('pay_channel_id');
            $show->field('amount');
            $show->field('date');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new OrdersLog(), function (Form $form) {
            $form->display('id');
            $form->text('user_id');
            $form->text('pay_channel_id');
            $form->text('amount');
            $form->text('date');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
