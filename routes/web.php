<?php

use App\Models\Order;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\BaseController;
use App\Http\Controllers\TestController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\alipay\PayController;
use App\Http\Controllers\alipay\AliPayController;
use App\Http\Controllers\GatePay\GatePayController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/



Route::get('/word', function () {
    return redirect('/pay.docx');
});

Route::get('/payWord', function () {
    return redirect('/pay2.0.docx');
});

Route::get('aaa', [TestController::class, 'aaa']);

Route::get('view', [TestController::class, 'view']);
Route::get('cash', [TestController::class, 'cash'])->name('收银台展示订单信息（定制），搭配 api/cashAddOrder 使用');

Route::get('userBasicDataIndv', [TestController::class, 'userBasicDataIndv'])->name('个人用户基本信息开户');
Route::get('userBusiOpen', [TestController::class, 'userBusiOpen'])->name('个人用户基本信息开户');
Route::get('paymentTradePay', [TestController::class, 'paymentTradePay'])->name('个人用户基本信息开户');
Route::get('tradeSettlementEnchashment', [TestController::class, 'tradeSettlementEnchashment'])->name('个人用户基本信息开户');
Route::get('userBusiModify', [TestController::class, 'userBusiModify'])->name('个人用户基本信息开户');
Route::get('tradeAcctpaymentBalanceQuery', [TestController::class, 'tradeAcctpaymentBalanceQuery'])->name('个人用户基本信息开户');


// Route::get('trade', [AliPayController::class, 'trade'])->name('下单');
// Route::get('TransferPlugin', [AliPayController::class, 'TransferPlugin'])->name('下单');

// GatePay 测试路由
Route::prefix('gatepay')->group(function () {
    // 基础签名测试
    Route::get('/test', [GatePayController::class, 'index'])->name('GatePay签名测试');

    // 专门测试创建订单
    Route::get('/test-create-order', [GatePayController::class, 'testCreateOrder'])->name('GatePay创建订单测试');

    // 获取支持的链列表
    Route::get('/chains/{currency?}', function ($currency = 'BTC') {
        $controller = new GatePayController();
        return response()->json($controller->getPayAddressChains($currency));
    })->name('获取支持的链列表');

    // 获取账户余额
    Route::get('/balance', function () {
        $controller = new GatePayController();
        return response()->json($controller->getAccountBalance());
    })->name('获取账户余额');

    // 查询订单状态
    Route::get('/order/{orderId}/{merchantTradeNo}', function ($orderId, $merchantTradeNo) {
        $controller = new GatePayController();
        return response()->json($controller->getOrderStatus($orderId, $merchantTradeNo));
    })->name('查询订单状态');
});
