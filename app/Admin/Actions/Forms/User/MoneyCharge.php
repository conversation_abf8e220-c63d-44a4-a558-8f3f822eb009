<?php
/*
 * @Author: your name
 * @Date: 2021-03-04 14:16:23
 * @LastEditTime: 2021-07-07 10:58:27
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \huoke\app\Admin\Actions\Forms\Order\Assistant.php
 */

namespace App\Admin\Actions\Forms\User;

use Dcat\Admin\Admin;
use App\Models\Message;
use App\Models\PayChannel;
use Dcat\Admin\Widgets\Form;
use App\Models\User;
use App\Models\PayChannelOrder;
use Dcat\Admin\Traits\LazyWidget;
use Illuminate\Support\Facades\DB;
use App\Models\UsersRechargeAmount;
use Illuminate\Support\Facades\Log;
use Dcat\Admin\Contracts\LazyRenderable;

class MoneyCharge extends Form implements LazyRenderable
{
    use LazyWidget;

    /**
     * 处理请求
     *
     * @param array $input
     * @return void
     */
    public function handle(array $input)
    {
        DB::beginTransaction();
        try {
            //code...
            $id = $input['id'] ?? null;
            $money = $input['money'] ?? 0;
            $withdraw_amount1 = $input['withdraw_amount1'] ?? 0;
            $freeze_amount1 = $input['freeze_amount1'] ?? 0;

            Log::channel('money_log')->info('>>>>>>>>>>>>>>>>>>>>');
            Log::channel('money_log')->info('用户充值');

            $mone = User::where(
                'id',
                $id
            )->value('recharge_amount');
            User::where('id', $id)->increment('recharge_amount', $money);
            $mtwo = User::where('id', $id)->value('recharge_amount');

            $wone = User::where(
                'id',
                $id
            )->value('withdraw_amount');
            User::where('id', $id)->increment('withdraw_amount', $withdraw_amount1);
            $wtwo = User::where('id', $id)->value('withdraw_amount');

            $fone = User::where(
                'id',
                $id
            )->value('freeze_amount');
            User::where('id', $id)->increment('freeze_amount', $freeze_amount1);
            $ftwo = User::where('id', $id)->value('freeze_amount');

            //
            Log::channel('money_log')->info('数据', [
                'id' => $id,
                'status' => '成功',
                '充值金额' => $money,
                '充值前金额' => $mone,
                '充值后金额' => $mtwo,
                '修改提现金额' => $withdraw_amount1,
                '提现前金额' => $wone,
                '提现后金额' => $wtwo,
                '修改冻结金额' => $freeze_amount1,
                '冻结前金额' => $fone,
                '冻结后金额' => $ftwo,
            ]);

            // 写入充值日志
            // UsersRechargeAmount::insert([
            //     'user_id' => $id,
            //     'money' => $money,
            //     'recharge_amount' => $mone,
            //     'recharge_amount_n' => $mtwo,
            //     'created_at' => now(),
            // ]);
            DB::commit();
            Log::channel('money_log')->info('结束');
            Log::channel('money_log')->info('<<<<<<<<<<<<<<<<<<<<');
            return $this->response()->success('充值成功')->refresh();
        } catch (\Throwable $th) {
            //throw $th;

            DB::rollBack();
            Log::channel('money_log')->info('结束', [$th->getMessage()]);
            Log::channel('money_log')->info('<<<<<<<<<<<<<<<<<<<<');
            return $this->response()->error('充值失败');
        }
    }

    /**
     * 创建表单
     *
     * @return void
     */
    public function form()
    {
        $this->decimal('money', '充值余额金额')->help('请不要轻易去修改: 新增or减少金额');
        $this->decimal('withdraw_amount1', '修改提现金额')->help('请不要轻易去修改: 新增or减少金额');
        $this->decimal('freeze_amount1', '修改冻结金额')->help('请不要轻易去修改: 新增or减少金额');
        $this->hidden('withdraw_usdt1', '修改提现usdt')->default(0);
        $this->hidden('recharge_usdt1', '修改usdt余额')->default(0);
        // 设置隐藏表单，传递用户id
        $this->hidden('id');
    }
}
