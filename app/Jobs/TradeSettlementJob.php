<?php
/**
 * 汇付天下取现
 */

namespace App\Jobs;

use Exception;
use App\Models\User;
use App\Models\PayChannel;
use App\Models\UserBbBank;
use App\Models\RechargeList;
use Illuminate\Bus\Queueable;
use App\Services\UserUpAmount;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use App\Http\Controllers\HuiFuController;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Contracts\Cache\LockTimeoutException;

class TradeSettlementJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $id;
    protected $business;

    public function __construct($id, int $business)
    {
        $this->id = $id;
        $this->business = $business;
    }

    public function handle(): void
    {

        try {
            Cache::lock(__CLASS__ . __FUNCTION__ . $this->business, 30)->block(60, function () {
                $this->runTask();
            });
        } catch (LockTimeoutException | Exception $exception) {
            Log::channel('recharge')->info('商户取现订单失败 ', [
                'line' => $exception->getLine(),
                'file' => $exception->getFile(),
                'code' => $exception->getCode(),
                'message' => $exception->getMessage(),
            ]);
        }
    }

    public function runTask()
    {
        $lock = Cache::lock(__CLASS__ . __FUNCTION__ . 'id:' . $this->id, 15);
        if (!$lock->get()) {
            return;
        }
        $result = RechargeList::query()->find($this->id);
        if (!$result || 2 != $result['status']) {
            return;
        }
        if (1 != $result['qx_status']) {
            return;
        }
        $data = $this->handleParams($result);
        // // 绑定的汇付天下商户通道
        // $pay_channel_id = User::where('agent_id', $data['user_id'])->value('pay_channel_id');
        $this->huiFu($data);

        $lock->release();

    }

    // 构建订单参数
    public function handleParams($params)
    {
        return [
            'id' => $params['id'],
            'order_id' => $params['recharge_list_order'],
            'user_id' => $params['agent_id'],
            'bb_bank_id' => $params['bb_bank_id'],
            'amount' => $params['amount'],
            'pay_channel_id' => $params['pay_channel_id'],
        ];
    }

    // 汇付天下
    public function huiFu($arr)
    {
        $channel = 'recharge';
        $pay_channel_id = $arr['pay_channel_id'];
        $bb_bank_id = $arr['bb_bank_id'];
        $amount = $arr['amount'];
        try {
            Log::channel($channel)->info("通道:{$pay_channel_id} 提现卡ID:{$bb_bank_id} 订单号:{$arr['order_id']} 金额:{$amount}---取现开始申请");
            $UserBbBank = UserBbBank::where(['id' => $bb_bank_id])->first();

            $PayChannel = PayChannel::where(['id' => $pay_channel_id, 'channel_type' => 1, 'recharge_status' => 1])->first();
            $config = json_decode($PayChannel['dataJson'], true);

            $tradeSettlementEnchashment = (new HuiFuController())->tradeSettlementEnchashment(['amount' => $amount, "huifu_id" => $UserBbBank['huifu_id'], "token_no" => $UserBbBank['token_no']], $config, $arr['order_id']);
            Log::channel($channel)->info("通道:{$pay_channel_id} 提现卡ID:{$bb_bank_id} 订单号:{$arr['order_id']} 金额:{$amount}---返回信息", $tradeSettlementEnchashment);

            $trans_stat = $tradeSettlementEnchashment['trans_stat'];
            if ($tradeSettlementEnchashment['resp_code'] == '********' && in_array($trans_stat, ['S', 'F', 'P'])) {
                // 取现提交成功
                if ($trans_stat == 'S') {
                    // 成功
                    $qx_status = 2;
                    $str = '取现申请成功';
                    $UserBbBank->decrement('amount', $amount);
                    $UserBbBank->save();
                }
                if ($trans_stat == 'F') {
                    // 失败
                    $qx_status = 3;
                    $str = '取现申请失败';
                }
                if ($trans_stat == 'P') {
                    // 取现中
                    $qx_status = 4;
                    $str = '取现申请中等待回调';
                    // $UserBbBank->decrement('amount', $amount);
                    // $UserBbBank->save();
                }
                Log::channel($channel)->info("通道:{$pay_channel_id} 提现卡ID:{$bb_bank_id} 订单号:{$arr['order_id']} 金额:{$amount}---{$str}");
                RechargeList::where(['id' => $arr['id']])->update(['qx_status' => $qx_status]);

            } else {
                Log::channel($channel)->info("通道:{$pay_channel_id} 提现卡ID:{$bb_bank_id} 订单号:{$arr['order_id']} 金额:{$amount}---取现申请失败", [$tradeSettlementEnchashment['resp_desc']]);
                RechargeList::where(['id' => $arr['id']])->update(['qx_status' => 3, 'error_msg' => $tradeSettlementEnchashment['resp_desc']]);
            }

        } catch (\Throwable $th) {
            // 代付申请失败返回申请金额
            Log::channel($channel)->info("通道:{$pay_channel_id} 提现卡ID:{$bb_bank_id} 订单号:{$arr['order_id']} 金额:{$amount}---取现申请异常", [$th->getMessage()]);
        }
    }
}
