<?php
/*
 * 商户绑定关系
 */

namespace App\Admin\Actions\Grid\User;

use Dcat\Admin\Widgets\Modal;
use Dcat\Admin\Grid\RowAction;
use App\Admin\Actions\Forms\User\Relationship as Form;

class Relationship extends RowAction
{
  protected $title = '<a href="javascript:void(0)" "><i class="fa fa-refresh"></i> 绑定关系</a>';

  public function render()
  {
    // 实例化表单类并传递自定义参数
    $form = Form::make()->payload(['id' => $this->getKey(), 'relationship_id' => $this->row->relationship_id, 'pay_channel_id' => $this->row->pay_channel_id]);

    return Modal::make()
      ->lg()
      ->title('绑定关系')
      ->body($form)
      ->button($this->title);
  }
}
