<?php
/*
 * @Author: your name
 * @Date: 2021-03-09 20:48:06
 * @LastEditTime: 2021-07-08 10:43:38
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \huoke\app\Models\AdminUser.php
 */

namespace App\Models;

use Dcat\Admin\Form;
use Dcat\Admin\Grid\Model;

use Dcat\Admin\Traits\ModelTree;
use Dcat\Admin\Models\Administrator;
use Illuminate\Notifications\Notifiable;
use App\Admin\Controllers\DataController;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Pagination\AbstractPaginator;
use Illuminate\Foundation\Auth\User as Authenticatable;

class AdminUser extends Authenticatable
{
    use Notifiable;
    use HasDateTimeFormatter;
    protected $table = 'admin_users';


    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name', 'email', 'password', 'api_token',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token', 'api_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function roles()
    {
        return $this->hasMany(AdminRoleUser::class, 'user_id', 'id');
    }

    /**
     * 编辑当前后台用户并排除当前数据不进行过滤
     *
     * @param [type] $query
     * @param [type] $id
     * @return void
     */
    public function scopeKfFilterIdNotInUser($query, $id)
    {
        $userIn = [];
        Administrator::whereHas('roles', function ($query) {
            //
            $query->where('slug', 'user_kefu');
        })->select(['admin_id'])->where('id', '<>', $id)->get()->map(function ($item) use (&$userIn) {
            //
            $userIn = array_merge($userIn, explode(',', $item->admin_id));
        });

        $userIn = array_unique($userIn);

        return $query->whereNotIn('id', $userIn);
    }

    /**
     * 查询出后台用户未绑定的商户
     *
     * @param [type] $query
     * @return void
     */
    public function scopeKfNotInUser($query)
    {
        $userIn = [];
        Administrator::whereHas('roles', function ($query) {
            //
            $query->where('slug', 'user_kefu');
        })->select(['admin_id'])->get()->map(function ($item) use (&$userIn) {
            //
            $userIn = array_merge($userIn, explode(',', $item->admin_id));
        });
        $userIn = array_unique($userIn);

        return $query->whereNotIn('id', $userIn);
    }
}
