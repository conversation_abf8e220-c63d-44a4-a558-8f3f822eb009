<?php
namespace App\Http\Controllers\kq;
// use remote\RemoteProcessor;

use Exception;
use App\Models\Order;
use App\Models\UsersBank;
use App\Models\PayChannel;
use App\Models\UserBbBank;
use remote\RemoteProcessor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Validator;

/**
 * 认证支付首次接口
 */

class FirstCertificationController extends BaseController
{
    /**
     * 消费签约申请
     * @param $rawData 前端传过来的json字符串
     * 
     * 返回数据
     *  [
     *     "head" => [
     *         "version" => "1.0.0",
     *         "messageType" => "A2001",
     *         "memberCode" => "***********",
     *         "responseCode" => "0000",
     *         "responseTextMessage" => "成功",
     *         "externalRefNumber" => "1733119048_cert_apply",
     *     ],
     *     "responseBody" => [
     *         "bizResponseCode" => "0000",
     *         "bizResponseMessage" => "交易成功",
     *         "merchantId" => "***************",
     *         "customerId" => "1",
     *         "payToken" => "8120000000002958088",
     *         "token" => "9007614121",
     *     ]
     * ];
     */
    public function A2001(Request $request)
    {
        DB::beginTransaction();
        try {
            global $config;

            $log = 'kq_log';
            Log::channel($log)->info('------------------------------------------------------------');
            Log::channel($log)->info('A2001接口接收到前端数据：', [$request->all()]);

            $validator = Validator::make($request->post(), [
                'order_id' => 'required|regex:/^[^<>&;]*$/',
                'num' => 'required|regex:/^\d{16,19}$/|not_regex:/[^0-9]/', // 假设银行卡号为16到19位数字
                'name' => 'required|regex:/^[\p{Han}a-zA-Z\s]+$/u', // 只允许汉字、字母和空格
                'idcard' => ['required', 'regex:/^(?:\d{15}|\d{17}[\dXx])$/'],
                'mobile_no' => 'required|regex:/^1[3-9]\d{9}$/i', // 手机号为11位数字，以1开头，第二位为3-9之间的数字
                'bank_type' => 'required|regex:/^[^<>&;]*$/',
            ], [
                'order_id.required' => '订单号不能为空',
                'num.required' => '银行卡号不能为空',
                'num.regex' => '银行卡号必须为16到19位数字',
                'name.required' => '持卡人不能为空',
                'name.regex' => '持卡人姓名只能包含汉字、字母和空格',
                'idcard.required' => '持卡人身份证不能为空',
                'idcard.regex' => '身份证号必须为15或18位数字或X/x',
                'mobile_no.required' => '银行预留手机号不能为空',
                'mobile_no.regex' => '手机号必须为11位数字，以1开头，第二位为3-9之间的数字',
                'bank_type.required' => '开户银行不能为空',
                'order_id.regex' => '订单号内容中不允许包含特殊字符',
            ]);

            if ($validator->fails() == true) {
                Log::channel($log)->info($validator->errors()->first());
                Log::channel($log)->info('失败');
                Log::channel($log)->info('------------------------------------------------------------');
                return $this->responseJson(400, $validator->errors()->first());
            }

            $order_id = $request->post('order_id', '');
            $num = $request->post('num', '');
            $name = $request->post('name', '');
            $idcard = $request->post('idcard', '');
            $mobile_no = $request->post('mobile_no', '');
            $bank_type = $request->post('bank_type', '');
            $Order = Order::where(['order_id' => $order_id, 'status' => 1, 'is_show' => 1])->first();
            if (!$Order) {
                throw new Exception('订单不存在或状态不正确');
            }

            $UserBbBankInfo = UserBbBank::where('num', $num)->where('pay_channel_id', $Order->pay_channel_id)->first();
            if ($UserBbBankInfo->attestation == 2) {
                throw new Exception('该卡已绑定，请勿重复绑定');
            }

            $dataJson = PayChannel::dataJson($Order->pay_channel_id);
            $config = array_merge($config, $dataJson);

            // 添加待绑定银行卡信息
            $UsersBank = UsersBank::updateOrCreate([
                'user_id' => $Order->user_id,
                'num' => $num,
                'name' => $name,
                'idcard' => $idcard,
                'mobile_no' => $mobile_no,
            ], [
                'user_id' => $Order->user_id,
                'num' => $num,
                'name' => $name,
                'idcard' => $idcard,
                'bank_type' => $bank_type,
                'mobile_no' => $mobile_no,
                'status' => 2,
            ]);
            $customerId = $UsersBank->id;

            $amount = $Order->amount;
            //开始组装报文
            $arrayData = [
                'protocolNo' => $order_id,
                'customerId' => $customerId,
                'pan' => $num,
                'cardHolderName' => $name,
                'cardHolderId' => $idcard,
                'phoneNo' => $mobile_no,
                'amount' => $amount * 100,
                'protocolVersion' => $order_id,
                'bankId' => $bank_type,
            ];

            //---1.组装明文head数组
            $request_Head_Array = array();
            $request_Head_Array['version'] = "1.0.0";
            $request_Head_Array['messageType'] = "A2001";
            $request_Head_Array['memberCode'] = $config['account']['memberCode'];
            $request_Head_Array['externalRefNumber'] = $arrayData['protocolVersion'];
            //---2.组装明文body数组
            $request_Body_Array['merchantId'] = $config['account']['merchantId'];
            $request_Body_Array['customerId'] = $arrayData['customerId'];
            $request_Body_Array['pan'] = $arrayData['pan'];
            $request_Body_Array['bankId'] = $arrayData['bankId'];
            $request_Body_Array['cardHolderName'] = $arrayData['cardHolderName'];
            $request_Body_Array['cardHolderId'] = $arrayData['cardHolderId'];
            $request_Body_Array['phoneNo'] = $arrayData['phoneNo'];
            $request_Body_Array['amount'] = $arrayData['amount'];
            $request_Body_Array['protocolVersion'] = $arrayData['protocolVersion'];
            $request_Body_Array['protocolNo'] = $arrayData['protocolNo'];

            /**
             * 请求快钱，获取返回的明文
             * 提醒注意：如涉及敏感信息，请加工处理后再给前端
             *
             */
            $responseDecryptMessage = RemoteProcessor::requestKQ($request_Head_Array, $request_Body_Array, $config);

            Log::channel($log)->info('A2001接口收到快钱返回明文数据：' . $responseDecryptMessage);
            Log::channel($log)->info('------------------------------------------------------------');

            $data = json_decode($responseDecryptMessage, true);
            if (!$data) {
                throw new Exception('配置错误');
            }
            $bizResponseCode = $data['responseBody']['bizResponseCode'];
            $bizResponseMessage = $data['responseBody']['bizResponseMessage'];
            if ('0000' != $bizResponseCode) {
                throw new Exception($bizResponseMessage);
            }

            $token = $data['responseBody']['token'];
            $payToken = isset($data['responseBody']['payToken']) ? $data['responseBody']['payToken'] : $UserBbBankInfo->token_no;

            // 添加待绑定银行卡信息
            UserBbBank::updateOrCreate([
                'bank_id' => $customerId,
                'user_id' => $Order->user_id,
                'pay_channel_id' => $Order->pay_channel_id,
                'num' => $num,
                'name' => $name,
                'idcard' => $idcard,
            ], [
                'user_id' => $Order->user_id,
                'bank_id' => $customerId,
                'pay_channel_id' => $Order->pay_channel_id,
                'num' => $num,
                'bank_type' => $bank_type,
                'name' => $name,
                'status' => 2,
                'idcard' => $idcard,
                'token_no' => $payToken,
            ]);

            DB::commit();
            return $this->responseJson(200, '成功', [
                'token' => $token,
                'payToken' => $payToken,
                // 'externalRefNumber' => $request_Head_Array['externalRefNumber'],
            ]);

        } catch (\Throwable $th) {
            //throw $th;
            Log::channel($log)->info('A2001接口失败：' . $th->getMessage(), [$th->getLine(), $th->getFile()]);
            Log::channel($log)->info('------------------------------------------------------------');
            DB::rollBack();
            return $this->responseJson(400, $th->getMessage());
        }
    }

    /**
     * 签约扣款验证
     * @param $rawData 前端传过来的json字符串
     * 
     * 返回数据
     * [
     *     "head" => [
     *         "version" => "1.0.0",
     *         "messageType" => "A2002",
     *         "memberCode" => "***********",
     *         "responseCode" => "0000",
     *         "responseTextMessage" => "成功",
     *         "externalRefNumber" => "1733119048_cert_apply",
     *     ],
     *     "responseBody" => [
     *         "bizResponseCode" => "0000",
     *         "bizResponseMessage" => "交易成功",
     *         "merchantId" => "***************",
     *         "terminalId" => "20190724",
     *         "customerId" => "1",
     *         "refNumber" => "110107342111",
     *     ]
     * ];
     */
    public function A2002(Request $request)
    {
        DB::beginTransaction();

        try {
            //code...
            global $config;
            $log = 'kq_log';
            Log::channel($log)->info('------------------------------------------------------------');
            Log::channel($log)->info('A2002接口接收到前端数据：', [$request->all()]);

            $validator = Validator::make($request->post(), [
                'order_id' => 'required|regex:/^[^<>&;]*$/',
                'token' => 'required|digits_between:9,12',
                'validCode' => 'required|digits:6',
                'payToken' => 'required|digits:19',
            ], [
                'order_id.required' => '订单号不能为空',
                'order_id.regex' => '订单号内容中不允许包含特殊字符',
                'token.required' => 'token不能为空',
                'token.digits_between' => 'token必须为9到12位数字',
                'validCode.required' => '验证码不能为空',
                'validCode.digits' => '验证码必须为6位数字',
                'payToken.required' => '支付token不能为空',
                'payToken.digits' => '支付token必须为19位数字',
            ]);

            if ($validator->fails() == true) {
                $errorMessage = $validator->errors()->first();
                Log::channel($log)->info($errorMessage);
                Log::channel($log)->info('失败');
                Log::channel($log)->info('------------------------------------------------------------');
                return $this->responseJson(400, $errorMessage);
            }

            $token = $request->post('token');
            $validCode = $request->post('validCode');
            $payToken = $request->post('payToken');
            $order_id = $request->post('order_id');

            $Order = Order::where(['order_id' => $order_id, 'status' => 1, 'is_show' => 1])->first();
            if (!$Order) {
                throw new Exception('订单不存在或状态不正确');
            }

            $UserBbBank = UserBbBank::where('token_no', $payToken)->first();
            if (!$UserBbBank) {
                throw new Exception('payToken不正确');
            }

            $dataJson = PayChannel::dataJson($Order->pay_channel_id);
            $config = array_merge($config, $dataJson);

            //开始组装报文
            $arrayData = [
                'externalRefNumber' => $order_id,
                'customerId' => $UserBbBank->bank_id,
                'pan' => $UserBbBank->num,
                'cardHolderName' => $UserBbBank->name,
                'cardHolderId' => $UserBbBank->idcard,
                'phoneNo' => UsersBank::where('id', $UserBbBank->bank_id)->value('mobile_no'),
                'amount' => $Order->amount * 100,
                'validCode' => $validCode,
                'token' => $token,
                'tr3Url' => rtrim(env('KQ_URL'), '/') . '/api/order/notice/kq',
            ];

            //---1.组装明文head数组
            $request_Head_Array = array();
            $request_Head_Array['version'] = "1.0.0";
            $request_Head_Array['messageType'] = "A2002";
            $request_Head_Array['memberCode'] = $config['account']['memberCode'];
            //注意，此处externalRefNumber须与A2001的externalRefNumber相同
            $request_Head_Array['externalRefNumber'] = $arrayData['externalRefNumber'];
            //---2.组装明文body数组
            $request_Body_Array['merchantId'] = $config['account']['merchantId'];
            $request_Body_Array['terminalId'] = $config['account']['terminalId'];
            $request_Body_Array['entryTime'] = date('YmdHis', time());
            $request_Body_Array['customerId'] = $arrayData['customerId'];
            $request_Body_Array['pan'] = $arrayData['pan'];
            $request_Body_Array['cardHolderName'] = $arrayData['cardHolderName'];
            $request_Body_Array['cardHolderId'] = $arrayData['cardHolderId'];
            $request_Body_Array['phoneNo'] = $arrayData['phoneNo'];
            $request_Body_Array['amount'] = $arrayData['amount'];
            $request_Body_Array['validCode'] = $arrayData['validCode'];
            $request_Body_Array['token'] = $arrayData['token'];
            $request_Body_Array['tr3Url'] = $arrayData['tr3Url'];
            // $request_Body_Array['ext'] = $order_id;
            /**
             * 请求快钱，获取返回的明文
             * 提醒注意：如涉及敏感信息，请加工处理后再给前端
             *
             */
            $responseDecryptMessage = remoteProcessor::requestKQ($request_Head_Array, $request_Body_Array, $config);
            Log::channel($log)->info('A2002接口收到快钱返回明文数据：' . $responseDecryptMessage);
            Log::channel($log)->info('------------------------------------------------------------');

            $data = json_decode($responseDecryptMessage, true);
            if (!$data) {
                throw new Exception('配置错误');
            }

            $bizResponseCode = $data['responseBody']['bizResponseCode'];
            $bizResponseMessage = $data['responseBody']['bizResponseMessage'];
            if ('0000' != $bizResponseCode) {
                throw new Exception($bizResponseMessage);
            }

            $Order->is_show = 2;
            $Order->error_msg = '支付成功，请等待通知';
            $Order->save();

            $UserBbBank->attestation = 2;
            $UserBbBank->save();

            UsersBank::where('id', $UserBbBank->bank_id)->update(['attestation' => 2]);

            DB::commit();

            return $this->responseJson(200, '支付成功', []);
        } catch (\Throwable $th) {
            //throw $th;
            Log::channel($log)->info('A2002接口失败：' . $th->getMessage(), [$th->getLine(), $th->getFile()]);
            Log::channel($log)->info('------------------------------------------------------------');
            DB::rollBack();
            return $this->responseJson(400, $th->getMessage());
        }
    }


    /**
     * 查询余额
     * @param \Illuminate\Http\Request $request
     * @throws \Exception
     * @return 
     * * [
     *     "head" => [],
     *     "responseBody" => [
     *         "bizResponseCode" => "0000",
     *         "bizResponseMessage" => "交易成功",
     *         "acctBalanceList" => [
     *             0 => [
     *                 "memberCode" => ***********,
     *                 "acctType" => 1,
     *                 "balance" => 257865019423,
     *             ]
     *         ]
     *     ]
     * ];
     */
    public static function M0001($id, $configArr = '')
    {
        global $config;
        try {
            //code...
            $dataJson = PayChannel::dataJson($id);
            if (!$config) {
                $config = array_merge($configArr, $dataJson);
            } else {
                $config = array_merge($config, $dataJson);
            }

            //---1.组装明文head数组
            $request_Head_Array = array();
            $request_Head_Array['version'] = "1.0.0";
            $request_Head_Array['messageType'] = "M0001";
            $request_Head_Array['memberCode'] = $config['account']['memberCode'];
            //注意，此处externalRefNumber须与A2001的externalRefNumber相同
            $request_Head_Array['externalRefNumber'] = time() . "_cnp_apply";
            //---2.组装明文body数组
            $request_Body_Array['targetMemberCode'] = $config['account']['memberCode'];
            $request_Body_Array['acctTypeList'] = [1];

            /**
             * 请求快钱，获取返回的明文
             * 提醒注意：如涉及敏感信息，请加工处理后再给前端
             *
             */
            $responseDecryptMessage = remoteProcessor::requestKQ($request_Head_Array, $request_Body_Array, $config);

            $data = json_decode($responseDecryptMessage, true);

            if (!$data) {
                throw new Exception('配置错误');
            }

            $bizResponseCode = $data['responseBody']['bizResponseCode'];
            $bizResponseMessage = $data['responseBody']['bizResponseMessage'];
            if ('0000' != $bizResponseCode) {
                throw new Exception($bizResponseMessage);
            }

            $acctBalanceList = $data['responseBody']['acctBalanceList'];
            $balance = $acctBalanceList[0]['balance'] / 100;

            PayChannel::where('id', $id)->update(['avl_bal' => $balance]);
            return [
                'avl_bal' => $balance, // 可用余额	
            ];
        } catch (\Throwable $th) {
            //throw $th;
            return [
                'avl_bal' => 0, // 可用余额	
            ];
        }
    }

}