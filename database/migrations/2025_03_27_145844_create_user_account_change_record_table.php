<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUserAccountChangeRecordTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_account_change_record', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('user_id')->nullable()->comment('商户号');
            $table->integer('type')->default('1')->comment('变化类型 1代发 2充值 3退款');
            $table->string('service_charge')->default('0')->comment('服务费');
            $table->string('amount')->default('0')->comment('金额');
            $table->string('money_q')->default('0')->comment('账变前');
            $table->string('money_h')->default('0')->comment('账变后');
            $table->string('order_id')->nullable()->comment('订单号');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_account_change_record');
    }
}
