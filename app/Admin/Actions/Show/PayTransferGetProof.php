<?php
/*
 * @Author: your name
 * @Date: 2021-03-08 17:29:20
 * @LastEditTime: 2021-07-08 17:55:00
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \huoke\app\Admin\Actions\NoRecommendUser.php
 */

namespace App\Admin\Actions\Show;

use App\Models\User;
use Dcat\Admin\Admin;
use App\Models\Message;
use App\Models\Recharge;
use App\Models\PayChannel;
use App\Models\RechargeList;
use Illuminate\Http\Request;
use Dcat\Admin\Actions\Action;
use Dcat\Admin\Actions\Response;
use App\Http\Controllers\kq\KqNotifyController;
use App\Http\Controllers\Api\HuiFuBaoController;

class PayTransferGetProof extends Action
{
    /**
     * @return string
     */
    protected $title = '<i class="fa fa-first-order"></i> 付款凭证下载';

    protected $modalId = 'show-PayTransferGetProof-user';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        $id = $this->getKey();
        $RechargeList = RechargeList::where('recharge_id', $id)->whereIn('qx_status', [2])->first(['pdf_url', 'pay_channel_id', 'recharge_list_order']);

        if ($RechargeList) {
            //
            if (isset($RechargeList['pdf_url'])) {
                return $this->response()->download(rtrim(env('KQ_URL'), '/') . $RechargeList['pdf_url']);
            }

            $pay_channel_id = $RechargeList['pay_channel_id'];

            $data = PayChannel::where('id', $pay_channel_id)->first(['channel_type', 'dataJson']);
            if ($data['channel_type'] == 2) {
                // 获取快钱的凭证下载地址
                $result = KqNotifyController::C1022(['externalRefNumber' => $RechargeList['recharge_list_order'], 'pay_channel_id' => $pay_channel_id]);

                // 获取响应内容
                if ($result != 400) {
                    $file = $result;
                    return $this->response()->download(rtrim(env('KQ_URL'), '/') . $file);
                }
                return $this->response()->error('下载失败.');
            }
        }
        return $this->response()->error('下载失败.');
    }

    public function confirm()
    {
        return ['你确定下载付款凭证吗？'];
    }
}
