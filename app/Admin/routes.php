<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Dcat\Admin\Admin;

Admin::routes();

Route::group([
    'prefix' => config('admin.route.prefix'),
    'namespace' => config('admin.route.namespace'),
    'middleware' => config('admin.route.middleware'),
], function (Router $router) {

    $router->get('/', 'HomeController@index');
    $router->resource('users', 'UserController');
    $router->resource('clerk', 'UserClerkController');
    $router->resource('orders', 'OrderController');
    $router->resource('payChannel', 'PayChannelController');
    $router->resource('rechargeRate', 'RechargeRateController');
    $router->resource('recharge', 'RechargeController');
    $router->resource('orderLogs', 'OrdersLogController');
    $router->resource('user/accountlog', 'UserAccountChangeRecordController');

    $router->resource('auth/agent', 'AdminUserController')->names('代理管理');
    $router->resource('auth/channel', 'AdminUserChanelController')->names('代理管理');
    $router->resource('user/bank', 'UsersBankController')->names('商户银行卡信息');
    $router->resource('user/bb/bank', 'UserBbBankController')->names('通道开户绑定');
    $router->resource('holiday', 'HolidayController')->names('节假日库');
    $router->resource('RechargeFail', 'RechargeFailController')->names('批量代付');


    $router->resource('channel/count', 'PayChannelCountController');

});
