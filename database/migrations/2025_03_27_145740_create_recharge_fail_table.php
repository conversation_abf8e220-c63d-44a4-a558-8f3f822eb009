<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateRechargeFailTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('recharge_fail', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('batch_no')->nullable()->comment('订单号');
            $table->string('user_id')->nullable()->comment('商户号');
            $table->decimal('batch_amt')->comment('付款总金额');
            $table->string('notify_url')->nullable()->comment('回调地址');
            $table->timestamp('pay_at')->nullable()->comment('转账时间');
            $table->longText('desc')->nullable()->comment('备注');
            $table->integer('status')->default('1')->nullable()->comment('1待处理 2提现成功 3失败 4部分成功');
            $table->string('user_status')->nullable()->comment('用户响应  success 响应成功，其他的每隔 5分钟回调一次，共3次');
            $table->string('num')->nullable()->comment('银行卡号');
            $table->string('name')->nullable()->comment('姓名');
            $table->string('idcard')->nullable()->comment('身份证号');
            $table->integer('request_cout')->default('0')->nullable()->comment('尝试请求次数');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('recharge_fail');
    }
}
