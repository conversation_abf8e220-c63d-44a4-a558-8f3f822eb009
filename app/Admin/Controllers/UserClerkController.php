<?php

namespace App\Admin\Controllers;

use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use App\Models\UserClerk;
use Dcat\Admin\Http\Controllers\AdminController;

class UserClerkController extends AdminController
{
    protected $userArray = [];

    public function __construct()
    {
        $this->userArray = User::get()->pluck('name', 'id')->toArray();

    }


    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UserClerk(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('user_id')->using($this->userArray);
            $grid->column('name', '昵称');
            $grid->column('phone');
            $grid->column('password_plaintext');
            $grid->column('status');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->equal('user_id')->select($this->userArray);

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UserClerk(), function (Show $show) {
            $show->field('id');
            $show->field('user_id');
            $show->field('phone');
            $show->field('password');
            $show->field('status');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UserClerk(), function (Form $form) {
            $form->display('id');
            $form->select('user_id')->options($this->userArray);
            $form->text('name', '昵称');
            $form->mobile('phone');
            $id = $form->getKey();
            if ($id) {
                $form->password('password', trans('admin.password'))
                    ->minLength(5)
                    ->maxLength(20)
                    ->customFormat(function () {
                        return '';
                    });
            } else {
                $form->password('password', trans('admin.password'))
                    ->required()
                    ->minLength(5)
                    ->maxLength(20);
            }
            $form->password('password_confirmation', trans('admin.password_confirmation'))->same('password');

            $form->ignore(['password_confirmation']);
            $form->switch('status')->help('《-为禁用 为启用-》')->default(1);

            $form->hidden('password_plaintext');
            $form->display('created_at');
            $form->display('updated_at');
        })->saving(function (Form $form) {
            if ($form->password && $form->model()->get('password') != $form->password) {
                $form->password_plaintext = $form->password;
                $form->password = bcrypt($form->password);
            }

            if (!$form->password) {
                $form->deleteInput('password');
            }
        });
    }
}
