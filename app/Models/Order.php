<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'order';

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'agent_id');
    }

    public function channel()
    {
        return $this->belongsTo(PayChannel::class, 'pay_channel_id', 'id');
    }
}
