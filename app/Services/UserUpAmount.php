<?php

namespace App\Services;

use App\Models\OrdersLog;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use App\Models\UserAccountChangeRecord;

class UserUpAmount
{
    /**
     * 添加订单日志
     * @param mixed $user_id
     * @param mixed $pay_channel_id
     * @param mixed $amount
     * @param mixed $date
     * @return void
     */
    public static function AddOrderLog($user_id, $pay_channel_id, $amount = 0, $amount_san = 0, $date, $Issued_amount = 0)
    {
        //
        $ordersLog = OrdersLog::updateOrCreate([
            'user_id' => $user_id,
            'pay_channel_id' => $pay_channel_id,
            'date' => $date,
        ], [
            'user_id' => $user_id,
            'pay_channel_id' => $pay_channel_id,
            'date' => $date,
        ]);
        // 增加 amount 字段的值
        if ($amount > 0) {
            $ordersLog->count += 1;
        }
        $ordersLog->amount += $amount;
        $ordersLog->amount_san += $amount_san;

        if ($Issued_amount > 0) {
            $ordersLog->Issued_count += 1;
        }
        $ordersLog->Issued_amount += $Issued_amount;
        // 保存记录
        $ordersLog->save();
        return;
    }

    /**
     * 写入商户账变明细日志
     *
     * @param string $type 日志类型 1付款 2充值 3退款 4转账 5授信
     * @param string $user_id 商户id
     * @param integer $amount 操作金额
     * @param integer $service_charge 手续费
     * @param string $order_id 订单号
     * @param integer $money_q 账变前金额
     * @param integer $money_h 账变后金额
     */
    public static function AddUserAccountChangeRecord($type = 1, $user_id = '', $amount = 0, $service_charge = 0, $order_id = '', $money_q = 0, $money_h = 0, $channel = 'user_add_recharge')
    {
        //
        UserAccountChangeRecord::insert([
            'type' => $type,
            'user_id' => $user_id,
            'amount' => $amount,
            'service_charge' => $service_charge,
            'order_id' => $order_id,
            'money_q' => $money_q,
            'money_h' => $money_h,
            'created_at' => now()
        ]);

        Log::channel($channel)->info('商户账变记录日志：', ['商户id' => $user_id, '操作金额' => $amount, '手续费' => $service_charge, '账变前金额' => $money_q, '账变后金额' => $money_h, '订单号' => $order_id]);
    }
}
