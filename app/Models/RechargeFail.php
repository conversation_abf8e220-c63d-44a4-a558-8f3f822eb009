<?php

namespace App\Models;

use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;

class RechargeFail extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'recharge_fail';

    protected $fillable = ['batch_no', 'user_id', 'batch_amt', 'detail_data', 'notify_url', 'meta_data', 'desc', 'status', 'created_at', 'channel_type'];

    public function rechargeList()
    {
        return $this->hasMany(RechargeList::class, 'recharge_id', 'id');
    }

    public function rechargeListOne()
    {
        return $this->hasOne(RechargeList::class, 'recharge_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'agent_id');
    }

    public function recharge()
    {
        return $this->hasOne(Recharge::class, 'batch_no', 'batch_no');
    }

    public function getPdfPathAttribute($value)
    {
        if (!$value) {
            //
            return $value;
        }
        if (Str::contains($value, '//')) {
            return $value;
        }
        return 'http://' . $_SERVER['HTTP_HOST'] . '/storage/' . $value;
    }
}
