<?php
namespace App\Http\Controllers\kq;
// use remote\RemoteProcessor;

use Exception;
use util\PKIUtil;
use App\Models\Order;
use App\Models\UsersBank;
use App\Models\PayChannel;
use App\Models\UserBbBank;
use remote\RemoteProcessor;
use App\Models\RechargeList;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\NotifyController;

/**
 * 回调通知控制器
 */

class KqNotifyController extends BaseController
{

    /**
     * 快钱异步回调处理
     * @param mixed $arrayData
     * @param mixed $type
     * {"head":{"externalRefNumber":"2412021748332101929917","memberCode":"***********","messageType":"A9005","orgMessageType":"A2004","origMessageType":"A2004","version":"1.0.0"},"responseBody":{"amount":"1000","customerId":"29","merchantId":"***************","payToken":"8120000000002958641","refNumber":"************","settleMerchantId":"***************","terminalId":"********","bizResponseCode":"0000","bizResponseMessage":"交易成功"}}
     */
    public static function notify($arrayData = [], $type = '')
    {
        try {
            global $config;
            $log = 'kq_log';

            //---1.组装明文head数组
            $request_Head_Array = $arrayData['head'];
            $order_id = $request_Head_Array['externalRefNumber'];
            if ($type == 'order') {
                $pay_channel_id = Order::where('order_id', $order_id)->value('pay_channel_id');
            }
            if ($type == 'recharge') {
                $pay_channel_id = RechargeList::query()->where('recharge_list_order', $order_id)->value('pay_channel_id');
            }

            $dataJson = PayChannel::dataJson($pay_channel_id);
            $config = array_merge($config, $dataJson);

            $signedData = $arrayData['requestBody']['signedData'];
            $envelopedData = $arrayData['requestBody']['envelopedData'];
            // $salt = $request_Head_Array['memberCode'] . '_' . time();
            $salt = $order_id;
            $request_Body = PKIUtil::unseal($signedData, $envelopedData, $salt);
            $notifyDecryptMessage['head'] = $request_Head_Array;
            $notifyDecryptMessage['responseBody'] = json_decode($request_Body, true);

            Log::channel($log)->info('快钱异步通知明文：' . json_encode($notifyDecryptMessage, JSON_UNESCAPED_UNICODE));
            //开始自己的逻辑处理
            //TODO
            $responseBody = $notifyDecryptMessage['responseBody'];
            $bizResponseCode = $responseBody['bizResponseCode'];
            $txnStatus = isset($responseBody['txnStatus']) ? $responseBody['txnStatus'] : 'F';

            if (isset($responseBody)) {
                $status = 3; // 失败
                if ($bizResponseCode == 0000) {
                    // 成功
                    if ($type == 'order') {
                        $status = 2;
                    }
                    if ($type == 'recharge' && in_array($txnStatus, ['S', 'P'])) {
                        // xnStatus判断：
                        // 若值为S，则表明付款成功。
                        // 若值为P，则表明付款处理中，后续再次调用查询接口进行查证。
                        // 若值为F，则表明付款失败，再看明细的bizResponseCode和bizResponseMessage具体失败原因。
                        // 若值为R，则表明交易退票，即付款失
                        $status = 2;
                    }
                }

                $orderNum = isset($order_id) ? $order_id : '';
                $tradeNo = isset($responseBody['refNumber']) ? $responseBody['refNumber'] : '';
                $retMsg = isset($responseBody['bizResponseMessage']) ? $responseBody['bizResponseMessage'] : '下发失败';
                Log::channel($log)->info('回调数据--', [$orderNum, $status]);

                $arr = [
                    'orderNo' => $orderNum,
                    'tradeNo' => $tradeNo,
                    'status' => $status,
                    'retMsg' => $retMsg,
                ];
                if ($type == 'recharge') {
                    (new NotifyController())->rechargeNoticePayHandle($arr, 'kq');
                    if ($status == 2) {
                        // 下载回单
                        self::C1022(['externalRefNumber' => $order_id, 'pay_channel_id' => $pay_channel_id]);
                    }
                }
                if ($type == 'order') {
                    (new NotifyController())->orderNoticePayHandle($arr, '快钱支付');
                }
            }

            //开始响应快钱的通知，请勿删除，避免重复通知
            $response_Body_Array['memberCode'] = $arrayData['head']['memberCode'];
            $response_Body_Array['isReceived'] = '1';
            $body = json_encode($response_Body_Array, JSON_UNESCAPED_UNICODE);

            $response_Final['head'] = $arrayData['head'];
            $response_Final['responseBody'] = PKIUtil::seal($body, $salt);
            $respMessage = json_encode($response_Final, JSON_UNESCAPED_UNICODE);
            echo $respMessage;
        } catch (\Throwable $th) {
            //throw $th;
            Log::channel($log)->info('回调数据--', [$th->getMessage(), $th->getLine(), $th->getFile()]);
            return 'error';
        }
    }

    /**
     * 付款凭证查询
     * @param mixed $arr
     */
    static public function C1022($arr = [])
    {
        $log = 'kq_log';
        global $config;
        // 
        try {
            //code...
            $dataJson = PayChannel::dataJson($arr['pay_channel_id']);
            $config = array_merge($config, $dataJson);

            //接收form表单提交过来的数据并处理
            $request_Head_Array = array();
            $request_Body_Array = array();
            //1.组装明文head数组
            $request_Head_Array['version'] = "1.0.0";
            $request_Head_Array['messageType'] = "C1022";
            $request_Head_Array['memberCode'] = $config['account']['memberCode'];

            $request_Body_Array['externalRefNumber'] = $arr['externalRefNumber'];
            $responseDecryptMessage = RemoteProcessor::requestKQ($request_Head_Array, $request_Body_Array, $config);
            $data = json_decode($responseDecryptMessage, true);

            if (!$data) {
                throw new Exception('配置错误');
            }

            $responseBody = $data['responseBody'];
            $bizResponseCode = $responseBody['bizResponseCode'];//UMGW00309   UMGW00245
            $bizResponseMessage = $responseBody['bizResponseMessage'];//UMGW00309   UMGW00245
            if ($bizResponseCode == 'UMGW00309') {
                $base64String = $responseBody['voucherFileStream'];
                $outputPath = 'kq/' . date('Ymd') . '/' . $arr['externalRefNumber']; // 不包括文件扩展名
                $savedImagePath = self::base64ToImage($base64String, $outputPath);
                RechargeList::where('recharge_list_order', $arr['externalRefNumber'])->update(['pdf_url' => $savedImagePath]);
                Log::channel($log)->info("回单{$arr['externalRefNumber']}下载成功", [$savedImagePath]);
                return $savedImagePath;
            }
            Log::channel($log)->info("回单{$arr['externalRefNumber']}下载失败", [$bizResponseMessage]);
            return 400;
        } catch (\Throwable $th) {
            //throw $th;
            Log::channel($log)->info("回单{$arr['externalRefNumber']}下载失败", [$th->getMessage(), $th->getLine(), $th->getFile()]);
            return 400;
        }
    }
    /**
     * base64 字符串转图片/pdf
     * @param mixed $base64String
     * @param mixed $outputPath
     * @throws \Exception
     * @return string
     */
    static public function base64ToImage($base64String, $outputPath)
    {
        // 去掉 data URI 的前缀（如果有）
        if (preg_match('/^data:image\/(\w+);base64,/', $base64String, $matches)) {
            $imageData = substr($base64String, strpos($base64String, ',') + 1);
            $mimeType = $matches[1];
        } else {
            $imageData = $base64String;
            $mimeType = 'pdf'; // 默认类型，可以根据实际情况调整
        }

        // 解码 base64 字符串
        $decodedData = base64_decode($imageData);

        // 检查解码是否成功
        if ($decodedData === false) {
            throw new Exception('Base64 解码失败');
        }

        // 根据 MIME 类型确定文件扩展名
        switch ($mimeType) {
            case 'pdf':
                $extension = 'pdf';
                break;
            case 'jpg':
                $extension = 'jpg';
                break;
            case 'png':
                $extension = 'png';
                break;
            case 'gif':
                $extension = 'gif';
                break;
            default:
                throw new Exception('不支持的 MIME 类型');
        }

        // 保存图片文件到 storage/app/public 目录
        $fileName = $outputPath . '.' . $extension;
        Storage::disk('public')->put($fileName, $decodedData);

        return Storage::url($fileName);
    }
}