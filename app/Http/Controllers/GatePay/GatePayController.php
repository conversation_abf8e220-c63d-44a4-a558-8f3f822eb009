<?php

namespace App\Http\Controllers\GatePay;

use QL\QueryList;
use Carbon\Carbon;
use Yansongda\Pay\Pay;
use App\Models\PayChannel;
use App\Models\PaySubject;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Http;
use App\Http\Controllers\BaseController;
use Symfony\Component\HttpFoundation\Response;
use Yansongda\Pay\Plugin\Alipay\Fund\AccountQueryPlugin;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class GatePayController extends BaseController
{
    private $config;
    /**
     * 签名工具类常量
     */
    private const HMAC_SHA512 = 'sha512';
    public const INVALID_SIGNATURE = 'invalid signature';


    public function __construct()
    {
        $this->config = [
            'url' => 'https://openplatform.gateapi.io',
            'ClientId' => 'rnyQeYwjVgwTsnPJ',
            'secretKey' => 'JX7BzFrIH2ZA-vR2qJneTSmrf_LxdMkS960PgKHQsqE=',
            'userId' => '********'
        ];
    }



    /**
     * 生成签名（原Java方法：verifySignature）
     * @param string $timestamp 时间戳
     * @param string $nonce     随机字符串
     * @param string $body      请求体
     * @param string $secretKey 密钥
     * @return string 签名字符串
     */
    public function verifySignature($timestamp, $nonce, $body, $secretKey)
    {
        $payload = sprintf("%s\n%s\n%s\n", $timestamp, $nonce, $body);
        return $this->sign($payload, $secretKey);
    }

    /**
     * 对数据进行签名
     * @param string $signingData 要签名的数据
     * @param string $key         密钥
     * @return string 签名字符串
     */
    public function sign($signingData, $key)
    {
        try {
            $signature = hash_hmac(self::HMAC_SHA512, $signingData, $key, true);
            return $this->byteArrayToHexString($signature);
        } catch (\Exception $e) {
            throw new \RuntimeException("生成签名时出错: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * 将十六进制字符串转换为字节数组
     * @param string $hexString 十六进制字符串
     * @return string 字节数组
     */
    private function hexStringToByteArray($hexString)
    {
        if (strlen($hexString) % 2 !== 0) {
            throw new \InvalidArgumentException("十六进制字符串长度必须为偶数");
        }
        return hex2bin($hexString);
    }

    /**
     * 将字节数组转换为十六进制字符串
     * @param string $bytes 字节数组
     * @return string 十六进制字符串
     */
    private function byteArrayToHexString($bytes)
    {
        return bin2hex($bytes);
    }

    /**
     * 常量时间比较两个字节数组是否相等，防止时间侧信道攻击
     * @param string $a 第一个字节数组
     * @param string $b 第二个字节数组
     * @return bool 如果两个字节数组相等返回true，否则返回false
     */
    private function constantTimeEquals($a, $b)
    {
        if (strlen($a) !== strlen($b)) {
            return false;
        }

        $result = 0;
        $length = strlen($a);
        for ($i = 0; $i < $length; $i++) {
            $result |= ord($a[$i]) ^ ord($b[$i]);
        }
        return $result === 0;
    }

    /**
     * 验证签名是否有效
     * @param string $signingData 签名数据
     * @param string $signature   签名
     * @param string $key         密钥
     * @return string|null 如果签名有效返回null，否则返回错误信息
     */
    public function verify($signingData, $signature, $key)
    {
        try {
            $decodedSignature = $this->hexStringToByteArray($signature);
            $calculatedSignature = hash_hmac(self::HMAC_SHA512, $signingData, $key, true);

            if (!$this->constantTimeEquals($decodedSignature, $calculatedSignature)) {
                return self::INVALID_SIGNATURE;
            }

            return null;
        } catch (\Exception $e) {
            return self::INVALID_SIGNATURE;
        }
    }

    /**
     * 生成签名（Base64编码版本）
     * @param string $timestamp 时间戳
     * @param string $nonce     随机字符串
     * @param string $body      请求体
     * @param string $secretKey 密钥
     * @return string Base64编码的签名字符串
     */
    public function generateSignature($timestamp, $nonce, $body, $secretKey)
    {
        try {
            // 连接 timestamp, nonce 和 body
            $message = $timestamp . $nonce . $body;

            // 使用 HMAC-SHA512 计算签名
            $hash = hash_hmac(self::HMAC_SHA512, $message, $secretKey, true);

            // 使用 Base64 编码
            return base64_encode($hash);
        } catch (\Exception $e) {
            throw new \RuntimeException("生成签名失败: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * 验证Base64编码的签名
     * @param string $timestamp 时间戳
     * @param string $nonce     随机字符串
     * @param string $body      请求体
     * @param string $signature Base64编码的签名
     * @param string $secretKey 密钥
     * @return bool 签名是否有效
     */
    public function verifyBase64Signature($timestamp, $nonce, $body, $signature, $secretKey)
    {
        try {
            $expectedSignature = $this->generateSignature($timestamp, $nonce, $body, $secretKey);
            return $this->constantTimeEquals($signature, $expectedSignature);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 生成随机nonce字符串
     * @param int $length nonce长度，默认16
     * @return string 随机nonce字符串
     */
    public function generateNonce($length = 16)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $nonce = '';
        $charactersLength = strlen($characters);

        for ($i = 0; $i < $length; $i++) {
            $nonce .= $characters[random_int(0, $charactersLength - 1)];
        }

        return $nonce;
    }

    /**
     * 获取当前时间戳（毫秒）
     * @return string 毫秒时间戳
     */
    public function getCurrentTimestamp()
    {
        return (string) (time() * 1000);
    }

    /**
     * 获取N分钟后的毫秒时间戳
     * @param int $minutes 分钟数
     * @return int 毫秒时间戳
     */
    public function getMilliTimestampAfterNMinutes($minutes)
    {
        return (time() + ($minutes * 60)) * 1000;
    }

    /**
     * 发送GatePay API请求
     * @param string $method HTTP方法 (GET, POST, PUT, DELETE)
     * @param string $endpoint API端点 (例如: '/v1/pay/address/chains')
     * @param array $data 请求数据
     * @param array $queryParams 查询参数
     * @return array API响应结果
     */
    public function sendGatePayRequest($method = 'GET', $endpoint = '', $data = [], $queryParams = [])
    {
        try {
            // 生成请求参数
            $timestamp = $this->getCurrentTimestamp();
            $nonce = $this->generateNonce(10); // 生成10位随机nonce
            $body = empty($data) ? '' : json_encode($data);

            // 生成签名
            $signature = $this->verifySignature($timestamp, $nonce, $body, $this->config['secretKey']);

            // 构建完整URL
            $url = $this->config['url'] . $endpoint;
            if (!empty($queryParams)) {
                $url .= '?' . http_build_query($queryParams);
            }

            // 设置请求头
            $headers = [
                'Content-Type' => 'application/json',
                'X-GatePay-Timestamp' => $timestamp,
                'X-GatePay-Nonce' => $nonce,
                'X-GatePay-Signature' => $signature,
                'X-GatePay-Certificate-ClientId' => $this->config['ClientId'],
            ];

            // 记录请求日志
            Log::info('GatePay API 请求', [
                'method' => $method,
                'url' => $url,
                'headers' => $headers,
                'body' => $body
            ]);

            // 发送HTTP请求
            $response = Http::withHeaders($headers)->timeout(30);

            switch (strtoupper($method)) {
                case 'GET':
                    $result = $response->get($url);
                    break;
                case 'POST':
                    $result = $response->asJson()->post($url, $data);
                    break;
                default:
                    throw new \InvalidArgumentException("不支持的HTTP方法: $method");
            }

            // 记录响应日志
            Log::info('GatePay API 响应', [
                'status' => $result->status(),
                'response' => $result->json()
            ]);

            return [
                'success' => $result->successful(),
                'status' => $result->status(),
                'data' => $result->json(),
                // 'headers' => $result->headers()
            ];

        } catch (\Exception $e) {
            Log::error('GatePay API 请求失败', [
                'error' => $e->getMessage(),
                'method' => $method,
                'endpoint' => $endpoint
            ]);

            return [
                'success' => false,
                'status' => 500,
                'error' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 获取BTC支付地址链信息
     * @param string $currency 货币类型，默认BTC
     * @return array
     */
    public function getPayAddressChains($currency = 'BTC')
    {
        return $this->sendGatePayRequest('GET', '/v1/pay/address/chains', [], ['currency' => $currency]);
    }

    /**
     * 创建支付订单
     * @param array $orderData 订单数据
     * @return array
     */
    public function createPayOrder($orderData)
    {
        // 验证必需参数
        $requiredFields = ['merchantTradeNo', 'currency', 'orderAmount', 'env', 'goods', 'orderExpireTime', 'merchantUserId', 'chain', 'fullCurrType'];
        foreach ($requiredFields as $field) {
            if (!isset($orderData[$field])) {
                return [
                    'success' => false,
                    'status' => 400,
                    'error' => "缺少必需参数: {$field}",
                    'data' => null
                ];
            }
        }

        // 确保数据类型正确
        $orderData['merchantUserId'] = (int) $orderData['merchantUserId'];
        $orderData['orderExpireTime'] = (int) $orderData['orderExpireTime'];

        return $this->sendGatePayRequest('POST', '/v1/pay/address/create', $orderData);
    }

    /**
     * 查询订单状态
     * @param string $orderId 订单ID
     * @return array
     */
    public function getOrderStatus($orderId, $merchantTradeNo)
    {
        return $this->sendGatePayRequest('GET', "/v1/pay/address/query", [], ['prepayId' => $orderId, 'merchantTradeNo' => $merchantTradeNo]);
    }

    /**
     * 获取账户余额
     * @return array
     */
    public function getAccountBalance()
    {
        return $this->sendGatePayRequest('GET', '/v1/pay/balance/query');
    }

    public function index()
    {
        //
        $timestamp = "*************";
        $nonce = "abcd1234";
        $bodyArray = [
            'a' => 1,
            'b' => 2,
        ];
        $body = json_encode($bodyArray);
        $secretKey = $this->config['secretKey'];

        // 使用十六进制签名方法
        $signature = $this->verifySignature($timestamp, $nonce, $body, $secretKey);
        echo "十六进制签名: " . $signature;

        // 使用Base64签名方法
        $base64Signature = $this->generateSignature($timestamp, $nonce, $body, $secretKey);
        echo "<br>Base64签名: " . $base64Signature;

        // 验证Base64签名
        $isValid = $this->verifyBase64Signature($timestamp, $nonce, $body, $base64Signature, $secretKey);
        echo "<br>签名验证结果: " . ($isValid ? '有效' : '无效');

        // 生成随机nonce和当前时间戳示例
        $randomNonce = $this->generateNonce();
        $currentTimestamp = $this->getCurrentTimestamp();
        echo "<br>随机Nonce: " . $randomNonce;
        echo "<br>当前时间戳: " . $currentTimestamp;

        // 测试API请求
        echo "<br><br>=== API请求测试 ===<br>";
        $apiResult = $this->getPayAddressChains('USDT');
        echo "BTC地址链查询结果: <pre>" . json_encode($apiResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

        // 3. 测试创建支付订单（示例数据）
        echo "<h3>3. 创建支付订单（示例）</h3>";
        $orderData = [
            'merchantTradeNo' => 'ORDER_' . time() . '_' . rand(1000, 9999),
            'currency' => 'USDT',
            'orderAmount' => '100',
            'env' => [
                'terminalType' => 'WEB'
            ],
            'goods' => [
                'goodsName' => '测试商品',
                'goodsDetail' => '这是一个测试订单'
            ],
            'orderExpireTime' => $this->getMilliTimestampAfterNMinutes(10),
            'returnUrl' => 'https://www.test1.com',
            'cancelUrl' => 'https://www.test2.com',
            'merchantUserId' => (int) $this->config['userId'],
            'chain' => 'ETH',
            'fullCurrType' => 'USDT_ETH',
            // 'channelId' => $this->config['userId'],
        ];
        echo "<pre>" . json_encode($orderData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        $orderResult = $this->createPayOrder($orderData);
        echo "<pre>" . json_encode($orderResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

    }


    /**
     * 专门测试创建订单的方法
     * @return \Illuminate\Http\JsonResponse
     */
    public function testCreateOrder()
    {
        try {
            // 构建正确的订单数据
            $orderData = [
                'merchantTradeNo' => 'TEST_ORDER_' . time() . '_' . rand(1000, 9999),
                'currency' => 'USDT',
                'orderAmount' => '10.50',
                'env' => [
                    'terminalType' => 'WEB'
                ],
                'goods' => [
                    'goodsName' => '测试商品',
                    'goodsDetail' => '这是一个测试订单商品详情'
                ],
                'orderExpireTime' => $this->getMilliTimestampAfterNMinutes(10), // 10分钟后过期
                'returnUrl' => 'https://your-domain.com/success',
                'cancelUrl' => 'https://your-domain.com/cancel',
                'merchantUserId' => (int) $this->config['userId'],
                'chain' => 'ETH',
                'fullCurrType' => 'USDT_ETH',
                // 'channelId' => $this->config['userId'],
            ];

            // 记录请求数据
            Log::info('创建订单请求数据', $orderData);

            // 调用创建订单方法
            $result = $this->createPayOrder($orderData);

            return response()->json([
                'success' => true,
                'message' => '订单创建测试完成',
                'request_data' => $orderData,
                'response' => $result
            ]);

        } catch (\Exception $e) {
            Log::error('创建订单测试失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }








    public function payment($arr = [], $channel)
    {
        //
        try {
            //code...
            $dataJson = json_decode($arr['dataJson'], true);
            // self::configHandle($dataJson);

            $pay_channel_id = $arr['id'];
            $name = $arr['name'];
            // 收款限制额度
            $amount_limit = $arr['amount_limit'];
            $visitKey = 'user_channel_amount:' . date('d') . 'id:' . $pay_channel_id;
            $currentValue = Redis::get($visitKey);
            $amount = bcadd($currentValue, $arr['amount'], 2);
            if ($amount && $amount_limit && $amount <= $amount_limit) {
                //
                $data = $this->alipay($arr['order_id'], $arr['amount']);
                // 写入laravel日志
                Log::channel($channel)->info("通道{$pay_channel_id}:{$name}请求返回数据:", [$arr, $data['data']]);
                Log::channel($channel)->info("通道{$pay_channel_id}:{$name}请求返回数据:========== " . $data['msg']);

                if ($data['code'] == 200) {
                    Log::channel($channel)->info("通道{$pay_channel_id}:{$name}请求返回数据:========== 成功,返回数据");
                    return [
                        "code" => 200,
                        "pay_channel_id" => $pay_channel_id,
                        "pay_url" => $data['data'],
                    ];
                }
            } else {
                Log::channel($channel)->info("通道{$pay_channel_id}:{$name}请求返回数据:========== 通道额度限制");
            }
            Log::channel($channel)->info("通道{$pay_channel_id}:{$name}请求返回数据:========== 下单失败");
            return [
                "code" => 400,
            ];
        } catch (\Throwable $th) {
            //throw $th;
            return [
                "code" => 400,
            ];
        }
    }
}
