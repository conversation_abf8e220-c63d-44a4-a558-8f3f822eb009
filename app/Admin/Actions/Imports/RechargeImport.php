<?php

namespace App\Admin\Actions\Imports;

use App\Models\Users;
use Dcat\Admin\Admin;
use App\Models\RechargeFail;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Database\Eloquent\Model;
use Maatwebsite\Excel\Concerns\ToModel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use App\Models\RecommendData as DataModel;
use Illuminate\Contracts\Queue\ShouldQueue;
use PhpOffice\PhpSpreadsheet\Helper\Sample;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithStartRow;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwe<PERSON>ite\Excel\Imports\HeadingRowFormatter;
use PhpOffice\PhpSpreadsheet\Worksheet\MemoryDrawing;

class RechargeImport implements WithMultipleSheets
{
    private $arr;
    private $agent_id;
    private $notify_url;
    private $pay_channel_id;

    public function __construct($arr, $agent_id = 0, $notify_url = '', $pay_channel_id = 1)
    {
        $this->arr = $arr;
        $this->agent_id = $agent_id;
        $this->notify_url = $notify_url;
        $this->pay_channel_id = $pay_channel_id;
    }

    public function sheets(): array
    {
        //这里需要注意的是键，这个键可以是sheet表的名称，比如 'sheet1'=> new FirstSheetImport()
        return [
            0 => new FirstRechargeSheetImport($this->arr, $this->agent_id, $this->notify_url, $this->pay_channel_id),
        ];
    }
}
