<?php

namespace App\Admin\Actions\Forms\User;

use App\Models\User;
use Dcat\Admin\Admin;
use Dcat\Admin\Widgets\Form;
use App\Services\UserUpAmount;
use Dcat\Admin\Traits\LazyWidget;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Dcat\Admin\Contracts\LazyRenderable;
use App\Http\Controllers\Api\OrderController;

class Relationship extends Form implements LazyRenderable
{
    use LazyWidget;

    /**
     * 处理请求
     *
     * @param array $input
     * @return void
     */
    public function handle(array $input)
    {
        DB::beginTransaction();
        try {
            //code...
            $id = $input['id'] ?? null;
            $relationship_id = $input['relationship_id'] ?? null;

            if ($id == $relationship_id) {
                return $this->response()->error('不能绑定自己');
            }

            User::where('id', $id)->update(['relationship_id' => $relationship_id]);

            DB::commit();
            return $this->response()->success('绑定成功')->refresh();
        } catch (\Throwable $th) {
            //throw $th;

            DB::rollBack();
            return $this->response()->error('绑定失败');
        }
    }

    /**
     * 创建表单
     *
     * @return void
     */
    public function form()
    {
        // 
        $id = $this->payload['id'] ?? null;
        // $pay_channel_id = explode(',', $this->payload['pay_channel_id']) ?? null;
        // if ($pay_channel_id == 25) {
        //     $pay_channel_id = 26;
        // }
        $pay_channel_id = 8;
        $agent_id = User::whereRaw(DB::raw("FIND_IN_SET($pay_channel_id, pay_channel_id)"))->pluck('name', 'id');
        $this->select('relationship_id', '绑定商户')->options($agent_id)->required();
        // // 设置隐藏表单，传递用户id
        $this->hidden('id')->value();
    }

    // 返回表单数据，如不需要可以删除此方法
    public function default()
    {
        // 获取外部传递参数
        $id = $this->payload['id'] ?? null;

        $relationship_id = User::where('id', $id)->value('relationship_id');
        return [
            'relationship_id' => $relationship_id,
            'id' => $id
        ];
    }
}
