<?php

namespace App\Admin\Controllers;

use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use App\Models\Order;
use Dcat\Admin\Admin;
use App\Models\PayChannel;
use App\Admin\Actions\Grid\OrderSuccess;
use App\Admin\Actions\Show\OrderNotifyUrl;
use Dcat\Admin\Http\Controllers\AdminController;

class OrderController extends AdminController
{
    protected $user;
    protected $PayChannel;

    public function __construct()
    {
        $this->user = [1 => '汇付宝'];
        $this->PayChannel = PayChannel::pluck('name', 'id')->toArray();
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        if (Admin::user()->isRole('agent')) {
            //
            return $this->gridAgent();
        }

        return Grid::make(new Order(), function (Grid $grid) {
            $startE = request()->input('created_at');
            $grid->model()->when(!$startE, function ($query) {
                $query->whereBetween('created_at', [date('Y-m-d 00:00:00'), 'end' => date('Y-m-d H:i:s')]);
            })->orderByDesc('id');
            $grid->column('id')->sortable();
            $grid->column('pay_channel_id')->using($this->PayChannel);
            // 显示快捷编辑按钮
            $grid->showQuickEditButton();
            $grid->column('order_id');
            $grid->column('pay_order', '三方渠道号');
            $grid->column('user_id')->using(User::getUserName());
            $grid->column('batch_no');
            $grid->column('tradeType')->using(['A_NATIVE' => '支付宝', 'T_JSAPI' => '微信公众号', 'KQ_QUICK' => '快钱支付'])->label(['A_NATIVE' => '#163efcc7', 'T_JSAPI' => '#4bb44d', 'KQ_QUICK' => '#bb4774']);
            $grid->column('status')->using([1 => '未支付', 2 => '支付成功', 3 => '超时', 4 => '支付失败'])->dot([
                1 => '#fccb16',
                2 => 'success',
                3 => '#ee2746',
                4 => '#ee2746'
            ]);


            $grid->column('amount');

            $grid->column('desc');
            $grid->column('user_status')->help('用户响应  success 响应成功，其他的每隔 5分钟回调一次，共3次');
            $grid->column('user_notify_at');
            $grid->column('user_notify_num');

            $grid->column('created_at');
            $grid->column('amount_san');

            $grid->filter(function (Grid\Filter $filter) {
                // 展开过滤器
                $filter->expand();
                // 更改为 panel 布局
                $filter->panel();
                $filter->equal('id')->width(3);
                $filter->equal('user_id')->select(User::getUserName())->width(3);
                $filter->equal('pay_channel_id')->select($this->PayChannel)->width(3);
                $filter->equal('order_id')->width(3);
                $filter->equal('batch_no')->width(3);
                $filter->equal('tradeType')->select(['A_NATIVE' => '支付宝', 'T_JSAPI' => '微信公众号', 'KQ_QUICK' => '快钱支付'])->width(3);
                $filter->equal('status')->select([1 => '未支付', 2 => '支付成功', 3 => '超时', 4 => '支付失败'])->width(3);
                $filter->between('created_at')->datetime()->default([
                    'start' => date('Y-m-d 00:00:00'),
                    'end' => date('Y-m-d 23:59:59')
                ])->width(6);

            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($actions->row->status != 1) {
                    $actions->append(OrderNotifyUrl::make()->setKey($actions->row->id));
                }
                if ($actions->row->status == 1) {
                    $actions->append(OrderSuccess::make()->setKey($actions->row->order_id));
                }
            });
            $grid->disableCreateButton();
            $grid->disableViewButton();
            $grid->disableEditButton();
            $grid->enableDialogCreate();

            // 设置弹窗宽高，默认值为 '700px', '670px'
            $grid->setDialogFormDimensions('50%', '80%');

            // 开启字段选择器功能
            $grid->showColumnSelector();
            // 设置默认隐藏字段
            $grid->hideColumns(['user_status', 'user_notify_num', 'user_notify_at', 'desc']);

        });
    }
    protected function gridAgent()
    {
        return Grid::make(new Order(), function (Grid $grid) {
            $startE = request()->input('created_at');
            $grid->model()->when(!$startE, function ($query) {
                $query->whereBetween('created_at', [date('Y-m-d 00:00:00'), 'end' => date('Y-m-d H:i:s')]);
            })->orderByDesc('id');
            $grid->model()->where('user_id', User::where('id', Admin::user()->user_id)->value('agent_id'));
            $grid->disableDeleteButton();
            $grid->disableRowSelector();
            $grid->column('order_id');
            $grid->column('pay_order', '三方渠道号');
            $grid->column('user_id')->using(User::getUserName());
            $grid->column('batch_no');
            $grid->column('tradeType')->using(['A_NATIVE' => '支付宝', 'T_JSAPI' => '微信公众号', 'KQ_QUICK' => '快钱支付'])->label(['A_NATIVE' => '#163efcc7', 'T_JSAPI' => '#4bb44d', 'KQ_QUICK' => '#bb4774']);
            $grid->column('status')->using([1 => '未支付', 2 => '支付成功', 3 => '超时', 4 => '支付失败'])->dot([
                1 => '#fccb16',
                2 => 'success',
                3 => '#ee2746',
                4 => '#ee2746'
            ]);


            $grid->column('amount');

            // $grid->column('desc');
            // $grid->column('user_status')->help('用户响应  success 响应成功，其他的每隔 5分钟回调一次，共3次');
            // $grid->column('user_notify_at');
            // $grid->column('user_notify_num');

            $grid->column('created_at');
            $grid->column('amount_san');

            $grid->filter(function (Grid\Filter $filter) {
                // $filter->equal('id');
                // 展开过滤器
                $filter->expand();
                // 更改为 panel 布局
                $filter->panel();
                $filter->equal('order_id')->width(3);
                $filter->equal('batch_no')->width(3);
                $filter->equal('tradeType')->select(['A_NATIVE' => '支付宝', 'T_JSAPI' => '微信公众号'])->width(3);
                $filter->equal('status')->select([1 => '未支付', 2 => '支付成功', 3 => '超时', 4 => '支付失败'])->width(3);
                $filter->between('created_at')->datetime()->default([
                    'start' => date('Y-m-d 00:00:00'),
                    'end' => date('Y-m-d 23:59:59')
                ])->width(6);

            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($actions->row->status != 1) {
                    $actions->append(OrderNotifyUrl::make()->setKey($actions->row->id));
                }
            });
            $grid->disableCreateButton();
            $grid->disableViewButton();
            $grid->disableEditButton();
            $grid->enableDialogCreate();

            // 设置弹窗宽高，默认值为 '700px', '670px'
            $grid->setDialogFormDimensions('50%', '80%');

            // 开启字段选择器功能
            $grid->showColumnSelector();
            // 设置默认隐藏字段
            // $grid->hideColumns(['user_status', 'user_notify_num', 'user_notify_at', 'desc']);

        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Order(), function (Show $show) {
            $show->field('id');
            $show->field('pay_channel_id');
            $show->field('pay_channel_no');
            $show->field('amount');
            $show->field('user_id');
            $show->field('batch_no');
            $show->field('status')->as(function ($value) {
                //
                $arr = [1 => '未支付', 2 => '支付成功', 3 => '超时', 4 => '支付失败', 5 => '订单金额与实际不否', 6 => '未创建订单且成功回调'];
                return $arr[$value] ?? '未知';
            });
            $show->field('add_at');
            $show->field('pay_at');
            $show->field('pay_url');
            $show->field('desc');
            $show->field('goods_name');
            $show->field('return_url');
            $show->field('user_ip');
            $show->field('amount_san');
            $show->field('pay_type');
            $show->field('order_id');
            $show->field('user_status');
            $show->field('user_notify_at');
            $show->field('user_notify_num');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Order(), function (Form $form) {
            $form->display('id');
            $form->text('order_id');
            $form->select('pay_channel_id')->options($this->PayChannel);
            $form->text('pay_order');
            $form->text('user_id');
            $form->text('batch_no');
            $form->decimal('amount');
            $form->decimal('amount_san');
            $form->radio('status')->options([1 => '未支付', 2 => '支付成功', 4 => '支付失败']);
            $form->datetime('add_at');
            $form->datetime('pay_at');
            $form->textarea('error_msg', '通道错误信息');
            $form->text('goods_name');
            $form->text('user_ip');
            $form->text('tradeType')->options(['A_NATIVE' => '支付宝', 'T_JSAPI' => '微信公众号']);
            $form->text('user_status');
            $form->datetime('user_notify_at');
            $form->number('user_notify_num');
            $form->url('return_url');
            $form->textarea('qr_code');
            $form->url('notify_url', '回调地址');
            $form->textarea('desc', '回调响应信息');
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
