<?php

namespace App\Admin\Repositories;

use Dcat\Admin\Admin;
use App\Models\OrdersLog;
use Dcat\Admin\Grid\Model;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Repositories\QueryBuilderRepository;

class PayChannelCount extends QueryBuilderRepository
{
    protected $admin_id;

    public function __construct(array $id = [])
    {
        $this->admin_id = $id;
        if (Admin::user()->isRole('administrator')) { // 超级管理员
            $this->admin_id = [];
        }
    }

    // 设置你的主键名称
    protected $keyName = 'id';

    // 设置创建时间字段
    protected $createdAtColumn = 'create_time';

    // 设置更新时间字段
    protected $updatedAtColumn = 'update_time';

    // 返回表名
    public function getTable()
    {
        return 'PayChannelCount';
    }

    // 返回你的主键名称
    public function getKeyName()
    {
        return $this->keyName;
    }

    // 通过这个方法可以指定查询的字段，默认"*"
    public function getGridColumns()
    {
        return ['*'];
    }

    // 通过这个方法可以指定表单页查询的字段，默认"*"
    public function getFormColumns()
    {
        return ['*'];
    }

    // 通过这个方法可以指定数据详情页查询的字段，默认"*"
    public function getDetailColumns()
    {
        return ['*'];
    }


    // 返回数据表格 Grid 的数据，用于数据表格展示
    // 例子中进行分页，不分页请参考官方文档
    public function get(Model $model)
    {
        // 获取当前页数
        $currentPage = $model->getCurrentPage();
        // 获取每页显示行数
        $perPage = $model->getPerPage();

        $start = ($currentPage - 1) * $perPage;

        // 获取排序参数, 格式例如['id', 'asc', null]
        $sort = $model->getSort();

        // 获取筛选条件
        $id = $model->filter()->input('id');
        $pay_channel_id = $model->filter()->input('pay_channel_id');
        $timeStart = $model->filter()->input('time.start', date('Y-m-01 00:00:01'));
        $timeEnd = $model->filter()->input('time.end', date('Y-m-d 23:59:59'));
        $time = [
            'start' => $timeStart,
            'end' => $timeEnd,
        ];
        list($orderColumn, $orderType) = $model->getSort();

        // 获取规格选择器条件
        $gender = $model->filter()->input('_selector.gender');

        $param = [
            'sort' => $sort,
            'search' => ['id' => $id, 'pay_channel_id' => $pay_channel_id, 'time' => $time, 'orderColumn' => $orderColumn, 'orderType' => $orderType],
            'selector' => ['gender' => $gender],
            'per_page' => $perPage,
            'start' => $start
        ];

        $data = $this->getList($param);

        return $model->makePaginator(
            $data['total'] ?? 0, // 传入总记录数
            $data['subjects'] ?? [$id] // 传入数据二维数组
        );
    }

    // 获取列表数据
    public function getList(array $param)
    {
        $prePage = $param['per_page'] ?? 20;
        $start = $param['start'] ?? 0;

        $time = $param['search']['time'];
        $pay_channel_id = $param['search']['pay_channel_id'] ?? '';
        $admin_id = $this->admin_id;

        $note_monitor = OrdersLog::select([
            'pay_channel_id',
            'date',
            DB::raw('SUM(amount) as total_amount'),
            DB::raw('SUM(count) as total_count')
        ])
            ->when($time, function ($query) use ($time) {
                $query->whereBetween('created_at', [$time['start'], $time['end']]);

            })
            ->when($pay_channel_id, function ($query, $pay_channel_id) {
                $query->where('pay_channel_id', $pay_channel_id);
            })
            ->when($admin_id, function ($query, $admin_id) {
                $query->whereIn('pay_channel_id', $admin_id);
            })
            ->groupBy('pay_channel_id', 'date')->orderByDesc('date')
            ->get();

        // 计算列表总数
        $count = $note_monitor->count();
        // 获取列表
        $list = array_slice($note_monitor->toArray(), $start, $prePage);

        return [
            'total' => $count,
            'subjects' => $list,
        ];
    }
}
