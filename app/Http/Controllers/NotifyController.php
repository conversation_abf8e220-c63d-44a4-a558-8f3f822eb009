<?php
/*
 * 回调下游控制器
 */

namespace App\Http\Controllers;

use Exception;
use App\Models\User;
use App\Models\Order;
use App\Models\Recharge;
use App\Models\UsersBank;
use App\Models\PayChannel;
use App\Models\UserBbBank;
use App\Models\RechargeList;
use Illuminate\Http\Request;
use App\Services\UserUpAmount;
use App\Jobs\TradeSettlementJob;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use App\Http\Controllers\kq\KqNotifyController;

class NotifyController extends BaseController
{

    // {
//     "resp_desc": "支付成功",
//     "resp_code": "********",
//     "resp_data": "{\"req_seq_id\":\"2411081723252271484313\",\"hf_seq_id\":\"002900TOP4A241102161322P330ac13652000000\",\"resp_desc\":\"支付成功\",\"resp_code\":\"********\",\"trans_stat\":\"S\"}"
// }
    /**
     * 下发回调通知
     * @param mixed $channel
     * @param \Illuminate\Http\Request $request
     * @return string
     */
    public function rechargeNotice($channel, Request $request)
    {
        $params = $request->all();
        $log = 'recharge_notify';
        Log::channel($log)->info('下发回调数据', [$params]);
        try {
            // 汇付天下
            if ($channel == 'huifu') {
                $resp_data = json_decode($params['resp_data'], 1);

                $trans_status = $resp_data['trans_status'];
                if (isset($resp_data) && in_array($trans_status, ['S', 'F'])) {
                    $status = 3; // 失败
                    if ($resp_data['sub_resp_code'] == ******** && $trans_status == 'S') {
                        // 成功
                        $status = 2;
                    }
                    $orderNum = isset($resp_data['req_seq_id']) ? $resp_data['req_seq_id'] : '';
                    $tradeNo = isset($resp_data['hf_seq_id']) ? $resp_data['hf_seq_id'] : '';
                    $retMsg = isset($resp_data['sub_resp_desc']) ? $resp_data['sub_resp_desc'] : '下发失败';

                    $arr = [
                        'orderNo' => $orderNum,
                        'tradeNo' => $tradeNo,
                        'status' => $status,
                        'retMsg' => $retMsg,
                    ];

                    $this->rechargeNoticePayHandle($arr, 'huifu');
                    return 'success';
                }
            }

            if ($channel == 'kq') {
                KqNotifyController::notify($params, 'recharge');
            }

            if ($channel == 'anquanpay') {
                // {"transAmount":"1.0","reason":"1925103364206608385服务费余额不足，扣除余额【0.01】失败:1926931423389892610","batchNo":"BT202505261720262425-1","transactionStatus":"支付失败","appId":"APP-6144844118978304","aliQueryResponse":null,"aliPayResponse":null,"signSn":"19B2F6ED08631460D0D667B2DC461365","tradeId":"5838130718143884","tradeType":"支转支"}
                $transactionStatus = $params['transactionStatus'];


                if (isset($transactionStatus) && in_array($transactionStatus, ['支付成功', '支付失败'])) {
                    $status = 3; // 失败
                    if ($transactionStatus == '支付成功') {
                        // 成功
                        $status = 2;
                    }
                    $orderNum = isset($resp_data['tradeId']) ? $resp_data['tradeId'] : '';
                    $tradeNo = isset($resp_data['batchNo']) ? $resp_data['batchNo'] : '';
                    $retMsg = isset($resp_data['reason']) ? $resp_data['reason'] : '下发失败';

                    $arr = [
                        'orderNo' => $orderNum,
                        'tradeNo' => $tradeNo,
                        'status' => $status,
                        'retMsg' => $retMsg,
                    ];

                    $this->rechargeNoticePayHandle($arr, 'anquanpay');
                    return 'success';
                }
            }

            return 'success';
        } catch (\Throwable $th) {
            //throw $th;
            return 'success';
        }
    }


    /**
     * 下发公共方法
     * @param mixed $params
     * @param mixed $channel
     * @return bool|int
     */
    public function rechargeNoticePayHandle($params, $channel = '')
    {

        $orderNo = $params['orderNo'];
        $status = $params['status'];
        $log = 'recharge_notify';
        Log::channel($log)->info('下发回调--', [$orderNo, $status]);
        $orderInfo = RechargeList::query()->where('recharge_list_order', $orderNo)->first();
        if (!$orderInfo) {
            return 400;
        }
        if ($orderInfo['qx_status'] != 4) {
            return false;
        }
        $lock = Cache::lock($orderNo, 20);
        if (!$lock->get()) {
            return false;
        }

        DB::beginTransaction();
        try {
            // 成功
            $orderInfo->pay_at = date('Y-m-d H:i:s');
            $orderInfo->qx_status = $status;
            $orderInfo->error_msg = $params['retMsg'];
            $orderInfo->save();
            $Recharge = Recharge::where('id', $orderInfo['recharge_id'])->first();
            $amount = $orderInfo['amount'];
            $user_id = $orderInfo['agent_id'];

            if ($status == 2) {
                if ($channel == 'huifu') {
                    UserBbBank::where(['id' => $orderInfo['bb_bank_id']])->decrement('amount', $amount);
                    // 提现成功
                    $user_id = $orderInfo['agent_id'];
                    User::where('agent_id', $user_id)->update([
                        'freeze_amount' => DB::raw("freeze_amount - {$amount}"),
                        'withdraw_amount' => DB::raw("withdraw_amount + {$amount}"),
                    ]);
                }

                if (in_array($channel, ['kq', 'anquanpay'])) {
                    UsersBank::where('id', $orderInfo['bb_bank_id'])->update(['status' => 2, 'attestation' => 2]);
                    $freeze_amount = bcadd($amount, $Recharge['batch_rate_amt'], 2);
                    // 提现成功
                    User::where('agent_id', $user_id)->update([
                        'freeze_amount' => DB::raw("freeze_amount - {$freeze_amount}"),
                        'withdraw_amount' => DB::raw("withdraw_amount + {$amount}"),
                    ]);
                    $Recharge->status = 2;
                    $Recharge->error_msg = '下发核实成功';
                    $Recharge->pay_at = now();
                    $Recharge->save();
                }
                DB::commit();
                UserUpAmount::AddOrderLog($user_id, $orderInfo['pay_channel_id'], 0, 0, date('Ymd'), $orderInfo['amount']);
            }

            if ($status == 3) {
                if ($channel == 'huifu') {
                    // 取现
                    $orderInfo->recharge_list_order = generateOrderNumber();
                    $orderInfo->qx_status = 1;
                    $orderInfo->error_msg = '取现失败，自动尝试取现--' . $orderInfo->error_msg;
                    $orderInfo->save();
                    DB::commit();
                    dispatch(new TradeSettlementJob($orderInfo['id'], $orderInfo['agent_id']))->onQueue('TradeSettlement');
                }

                if ($channel == 'kq') {
                    // 取现
                    $orderInfo->qx_status = 3;
                    $orderInfo->error_msg = '快钱交易失败';
                    $orderInfo->save();

                    $Recharge->status = 3;
                    $Recharge->error_msg = '下发失败';
                    $Recharge->pay_at = now();
                    $Recharge->save();

                    $freeze_amount = bcadd($amount, $Recharge->batch_rate_amt, 2);
                    PayChannel::where('id', $orderInfo->pay_channel_id)->increment('avl_bal', $freeze_amount);

                    $this->refundRecharge([
                        'order_id' => $Recharge['order_id'],
                        'user_id' => $user_id,
                        'recharge_amount' => $amount,
                        'batch_rate_amt' => $Recharge->batch_rate_amt,
                    ]);
                    DB::commit();
                }

                if ($channel == 'anquanpay') {
                    // 退款
                    $orderInfo->qx_status = 3;
                    $orderInfo->error_msg = '安全付交易失败';
                    $orderInfo->save();

                    $Recharge->status = 3;
                    $Recharge->error_msg = '下发失败';
                    $Recharge->pay_at = now();
                    $Recharge->save();

                    $freeze_amount = bcadd($amount, $Recharge->batch_rate_amt, 2);
                    PayChannel::where('id', $orderInfo->pay_channel_id)->increment('avl_bal', $freeze_amount);

                    $this->refundRecharge([
                        'order_id' => $Recharge['order_id'],
                        'user_id' => $user_id,
                        'recharge_amount' => $amount,
                        'batch_rate_amt' => $Recharge->batch_rate_amt,
                    ]);
                    DB::commit();
                }
            }

        } catch (Exception $e) {
            DB::rollBack();
        }
        // 释放锁
        $lock->release();
        return true;
    }

    /**
     * 下发公共退款方法
     * @param mixed $error_msg
     * @param mixed $arr
     * @param mixed $channel
     * @return void
     */
    public function refundRecharge($arr, $channel = 'recharge_notify')
    {
        DB::beginTransaction();
        try {
            Log::channel($channel)->info($arr['order_id'] . '---退款申请');

            $user_id = $arr['user_id'];
            $recharge_amount = $arr['recharge_amount'];
            $freeze_amount = bcadd($recharge_amount, $arr['batch_rate_amt'], 2);
            $recharge_amount_q = User::where('agent_id', $user_id)->value('recharge_amount');
            $recharge_amount_h = bcadd($freeze_amount, $recharge_amount_q, 2);

            User::where('agent_id', $user_id)->update([
                'recharge_amount' => DB::raw("recharge_amount + {$freeze_amount}"),
                'freeze_amount' => DB::raw("freeze_amount - {$freeze_amount}"),
            ]);

            // 写入帐变记录
            UserUpAmount::AddUserAccountChangeRecord(
                3,
                $user_id,
                $recharge_amount,
                $arr['batch_rate_amt'],
                $arr['order_id'],
                $recharge_amount_q,
                $recharge_amount_h
            );
            DB::commit();
            Log::channel($channel)->info($arr['order_id'] . '---结束申请');

        } catch (Exception $e) {
            DB::rollBack();
            Log::channel($channel)->info('申请失败===回滚错误 ' . $arr['order_id'] . $e->getMessage());
        }
    }

    /**
     * 收款回调通知
     * @param mixed $channel
     * @param \Illuminate\Http\Request $request
     * @return
     */
    public function orderNotice($channel, Request $request)
    {
        $params = $request->all();
        $log = 'order_notify';
        Log::channel($log)->info('收款回调数据', [$params]);
        try {
            // 汇付天下
            if ($channel == 'huifu') {
                $resp_data = json_decode($params['resp_data'], 1);
                if (isset($resp_data)) {

                    $status = 4; // 失败
                    if ($resp_data['resp_code'] == ********) {
                        // 成功
                        $status = 2;
                    }
                    $orderNum = isset($resp_data['req_seq_id']) ? $resp_data['req_seq_id'] : '';
                    $tradeNo = isset($resp_data['hf_seq_id']) ? $resp_data['hf_seq_id'] : '';
                    $retMsg = isset($resp_data['resp_desc']) ? $resp_data['resp_desc'] : '收款失败';
                    Log::channel($log)->info('收款回调--', [$orderNum, $status]);

                    $arr = [
                        'orderNo' => $orderNum,
                        'tradeNo' => $tradeNo,
                        'status' => $status,
                        'retMsg' => $retMsg,
                    ];

                    $this->orderNoticePayHandle($arr, '汇付天下');
                    return 'success';
                }
            }

            if ($channel == 'kq') {
                KqNotifyController::notify($params, 'order');
            }

            if ($channel == 'alipay') {
                if (!in_array($params['trade_status'], ['TRADE_SUCCESS', 'TRADE_CLOSED'], true)) {
                    return 'success';
                }
                $status = 4; // 失败
                $retMsg = '收款失败';
                if ($params['trade_status'] == 'TRADE_SUCCESS') {
                    // 成功
                    $status = 2;
                    $retMsg = '收款成功';

                }
                $orderNum = isset($params['out_trade_no']) ? $params['out_trade_no'] : '';
                $tradeNo = isset($params['trade_no']) ? $params['trade_no'] : '';
                Log::channel($log)->info('收款回调--', [$orderNum, $status]);

                $arr = [
                    'orderNo' => $orderNum,
                    'tradeNo' => $tradeNo,
                    'status' => $status,
                    'retMsg' => $retMsg,
                ];

                $this->orderNoticePayHandle($arr, '支付宝官方');
            }
            return 'success';
        } catch (\Throwable $th) {
            //throw $th;
            return '页面响应失败';
        }
    }

    /**
     * 收款公共方法
     * @param mixed $params
     * @param mixed $payType
     * @return bool|int
     */
    public function orderNoticePayHandle($params, $payType = '')
    {
        $orderNo = $params['orderNo'];
        $tradeNo = $params['tradeNo'];


        $status = $params['status'];

        $orderInfo = Order::query()->where('order_id', $orderNo)->first();
        if (!$orderInfo) {
            return 400;
        }
        if ($orderInfo['status'] != 1) {
            return false;
        }
        $lock = Cache::lock($orderNo, 20);
        if (!$lock->get()) {
            return false;
        }

        DB::beginTransaction();
        try {
            // 成功
            $user_id = $orderInfo['user_id'];
            $real_amt_fen = $orderInfo['amount'];
            if ($status == 2) {

                $userInfo = User::where('agent_id', $user_id)->firstOrFail();
                $recharge_amount_q = $userInfo->recharge_amount;
                // 手续费
                $recharge_point = $userInfo->recharge_point;
                $service_charge = bcdiv(bcmul($real_amt_fen, $recharge_point, 2), 100, 2);
                $amount_san = bcsub($real_amt_fen, $service_charge, 2);
                $userInfo->increment('recharge_amount', $amount_san);
                $recharge_amount_h = $userInfo->recharge_amount;
                $userInfo->save();

                $pay_channel_id = $orderInfo->pay_channel_id;
                $orderInfo->pay_order = $tradeNo;
                $orderInfo->amount_san = $amount_san;
                $orderInfo->pay_at = date('Y-m-d H:i:s');
                $orderInfo->status = $status;
                $orderInfo->error_msg = $params['retMsg'];
                $orderInfo->save();
                $order_id = $orderInfo['order_id'];
                DB::commit();

                // 快钱通道维护收款金额
                PayChannel::where('id', $pay_channel_id)->whereIn('channel_type', [2])->increment('avl_bal', $amount_san);

                UserUpAmount::AddOrderLog($user_id, $pay_channel_id, $real_amt_fen, $amount_san, date('Ymd'));
                UserUpAmount::AddUserAccountChangeRecord(2, $user_id, $real_amt_fen, $service_charge, $order_id, $recharge_amount_q, $recharge_amount_h);

                // 增加通道每日收款额度
                userChannelAmount($pay_channel_id, $real_amt_fen, 'add');

                $getData = [
                    'order_id' => $orderNo,
                    'batch_no' => $orderInfo['batch_no'],
                    'amount' => $orderInfo['amount'],
                    'status' => $orderInfo['status'],
                    'agent_id' => $user_id,
                    'pay_at' => $orderInfo['pay_at'],
                ];

                // 用户回调地址
                $url = $orderInfo['notify_url'];
                $id = $orderInfo['id'];
                $num = $orderInfo['user_notify_num'];
                Log::channel('user_notify')->info('处理数据', [$getData, $url, $id, $num]);
                // 商户key
                NotifyController::orderNotify($getData, $id, $url, $num, $userInfo['key']);

            }
            if ($status == 4) {
                $orderInfo->pay_order = $tradeNo;
                $orderInfo->pay_at = date('Y-m-d H:i:s');
                $orderInfo->status = $status;
                $orderInfo->error_msg = $params['retMsg'];
                $orderInfo->save();
                DB::commit();
            }
        } catch (Exception $e) {
            DB::rollBack();
        }
        // 释放锁
        $lock->release();
        return true;
    }

    /**
     * 下游商户回调
     *
     * @param [array] $getData 回调数据
     * @param [int] $id 订单号
     * @param [string] $url 商户号回调地址
     * @param [int] $num 回调次数
     * @param string $key 下游商户密钥
     * @return string
     */
    static function orderNotify($getData, $id = 0, $url = '', $num = 0, $key = '')
    {
        try {
            $channel = 'user_notify';
            $num = $num + 1;
            // 用户回调开始
            Log::channel($channel)->info('>>>>>>>>>>>>>>>>>>>>');
            Log::channel($channel)->info('开始');
            Log::channel($channel)->info('商户订单', [$getData['batch_no']]);
            // $client = new Client(['verify' => false]);

            $getData['key'] = $key;
            ksort($getData);
            $str = '';
            foreach ($getData as $k => $v) {
                $str .= $k . '=' . $v . '&';
            }
            // $str = $str . "key=$key";
            $str = mb_substr($str, 0, -1);

            $signature = md5($str);
            $getData['sign'] = $signature;

            unset($getData['key']);

            $remainingBytes = Http::post(
                $url,
                $getData
            )->body();
            if ($remainingBytes == 'success') {
                //
                Order::where('id', $id)->update([
                    'user_status' => 'success',
                    'user_notify_num' => $num,
                    'desc' => json_encode($remainingBytes, JSON_UNESCAPED_UNICODE),
                    'user_notify_at' => now()
                ]);
            } else {
                Order::where('id', $id)->update([
                    'user_status' => 'error',
                    'user_notify_num' => $num,
                    'desc' => json_encode($remainingBytes, JSON_UNESCAPED_UNICODE),
                    'user_notify_at' => now()
                ]);
            }
            Log::channel($channel)->info('响应次数：' . $num);
            Log::channel($channel)->info('响应数据', [$getData, $remainingBytes]);
            Log::channel($channel)->info('结束');
            Log::channel($channel)->info('<<<<<<<<<<<<<<<<<<<<');
            return $remainingBytes;
        } catch (\Throwable $th) {
            //throw $th;
            Order::where('id', $id)->update([
                'user_status' => '页面响应失败',
                'user_notify_num' => $num,
                'user_notify_at' => now()
            ]);
            Log::channel($channel)->info('页面响应失败');
            Log::channel($channel)->info('<<<<<<<<<<<<<<<<<<<<');
            return '页面响应失败';
        }
    }

    /**
     * 下游商户回调
     *
     * @param [array] $getData 回调数据
     * @param [int] $id 订单号
     * @param [string] $url 商户号回调地址
     * @param [int] $num 回调次数
     * @param string $key 下游商户密钥
     * @return string
     */
    static function rechargeNotify($getData, $id = 0, $url = '', $num = 0, $key = '')
    {
        try {
            $channel = 'user_notify';
            $num = $num + 1;
            // 用户回调开始
            Log::channel($channel)->info('>>>>>>>>>>>>>>>>>>>>');
            Log::channel($channel)->info('下发通知开始');
            Log::channel($channel)->info('下发通知商户订单', [$getData['batch_no']]);
            // $client = new Client(['verify' => false]);

            $getData['key'] = $key;
            ksort($getData);
            $str = '';
            foreach ($getData as $k => $v) {
                $str .= $k . '=' . $v . '&';
            }
            // $str = $str . "key=$key";
            $str = mb_substr($str, 0, -1);

            $signature = md5($str);
            $getData['sign'] = $signature;

            unset($getData['key']);

            $remainingBytes = Http::post(
                $url,
                $getData
            )->body();
            if ($remainingBytes == 'success') {
                //
                Recharge::where('id', $id)->update([
                    'user_status' => 'success',
                    'user_notify_num' => $num,
                    'desc' => json_encode($remainingBytes, JSON_UNESCAPED_UNICODE),
                    'user_notify_at' => now()
                ]);
            } else {
                Recharge::where('id', $id)->update([
                    'user_status' => 'error',
                    'user_notify_num' => $num,
                    'desc' => json_encode($remainingBytes, JSON_UNESCAPED_UNICODE),
                    'user_notify_at' => now()
                ]);
            }
            Log::channel($channel)->info('下发通知响应次数：' . $num);
            Log::channel($channel)->info('下发通知响应数据', [$getData, $remainingBytes]);
            Log::channel($channel)->info('下发通知结束');
            Log::channel($channel)->info('<<<<<<<<<<<<<<<<<<<<');
            return $remainingBytes;
        } catch (\Throwable $th) {
            //throw $th;
            Recharge::where('id', $id)->update([
                'user_status' => '页面响应失败',
                'user_notify_num' => $num,
                'user_notify_at' => now()
            ]);
            Log::channel($channel)->info('下发通知页面响应失败');
            Log::channel($channel)->info('<<<<<<<<<<<<<<<<<<<<');
            return '页面响应失败';
        }
    }
    /**
     * Summary of wxOrderErrorNotice
     * 微信收款超时通知
     * @return void
     */
    public function wxOrderErrorNotice()
    {
        //
        Order::where(['status' => 1, 'tradeType' => 'T_JSAPI', 'pay_channel_id' => 0])
            ->where('created_at', '<', now()->subMinutes(5))
            ->update(['status' => 4, 'pay_at' => now(), 'error_msg' => '订单超时未支付']);
        Order::where(['status' => 1, 'tradeType' => 'KQ_QUICK', 'is_show' => 1])
            ->where('created_at', '<', now()->subMinutes(6))
            ->update(['status' => 4, 'pay_at' => now(), 'error_msg' => '订单超时未支付']);
    }

    /**
     * 快钱余额A划转到快钱B
     * @return void
     */
    public function balanceKqTransfer()
    {
        $avl_bal = PayChannel::where('id', 7)->value('avl_bal');
        PayChannel::where('id', 7)->decrement('avl_bal', $avl_bal);
        PayChannel::where('id', 8)->increment('avl_bal', $avl_bal);
        echo date('Y-m-d H:i:s') . '快钱余额A划转到快钱B ok';
    }
}
