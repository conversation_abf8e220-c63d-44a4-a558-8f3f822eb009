<?php
/*
 * @Author: your name
 * @Date: 2021-04-01 17:06:30
 * @LastEditTime: 2021-05-21 14:43:25
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \huoke\app\Admin\Actions\Show\NoBankLeader.php
 */

namespace App\Admin\Actions\Show;

use App\Models\User;
use App\Models\Order;
use App\Models\Recharge;
use Illuminate\Http\Request;
use Dcat\Admin\Actions\Action;
use Dcat\Admin\Actions\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\NotifyController;
use App\Models\RechargeList;

class RechargeSuccess extends Action
{
    /**
     * @return string
     * 手动回调
     */
    protected $title = '<i class="fa fa-check" style="color: green;"> 手动成功</i> ';

    protected $modalId = 'show-RechargeSuccess-user';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle()
    {

        $id = $this->getKey();

        try {
            //code...
            Log::channel('user_notify')->info('>>>>>>>>下发手动处理下发成功开始>>>>>>>>');

            $Recharge = Recharge::query()->where('id', $id)->where('status', 1)->first();
            if ($Recharge) {
                $amount = $Recharge['batch_amt'];
                $user_id = $Recharge['user_id'];
                $freeze_amount = bcadd($amount, $Recharge['batch_rate_amt'], 2);
                // 提现成功
                User::where('agent_id', $user_id)->update([
                    'freeze_amount' => DB::raw("freeze_amount - {$freeze_amount}"),
                    'withdraw_amount' => DB::raw("withdraw_amount + {$amount}"),
                ]);
                $Recharge->status = 2;
                $Recharge->error_msg = '手动处理成功';
                $Recharge->pay_at = now();
                $Recharge->save();

                RechargeList::where('recharge_id', $id)->update(['status' => 2, 'qx_status' => 2, 'pay_at' => now(), 'error_msg' => '手动处理成功']);
                Log::channel('user_notify')->info('<<<<<<<<下发手动处理下发成功结束<<<<<<<<');
                return $this->response()->success('手动付款成功.')->refresh();
            }
            Log::channel('user_notify')->info('<<<<<<<<下发手动处理下发成功结束<<<<<<<<');
            return $this->response()->error('该订单不存在.');
        } catch (\Throwable $th) {
            Log::channel('user_notify')->info(
                '响应错误',
                [$th->getMessage()]
            );
            Log::channel('user_notify')->info('<<<<<<<<下发手动处理下发成功结束<<<<<<<<');
            return $this->response()->error('手动付款失败.');
        }
    }

    public function confirm()
    {
        return ['确定要处理成功吗？'];
    }
}
