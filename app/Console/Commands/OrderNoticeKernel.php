<?php

/**
 * Order表定时回调
 */
namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\Order;
use App\Models\Matching;
use App\Models\MatchingOrder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\NotifyController;
use App\Http\Controllers\Api\MatchingOrderController;
use App\Models\User;

class OrderNoticeKernel extends Command
{
    protected $signature = 'order:notice';
    protected $description = 'Order订单定时回调';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        while (true) {
            self::do();
        }
    }

    public static function do()
    {
        try {
            //code...
            // 默认5分钟
            $overtime = 300;
            $overtime = -$overtime;
            $data = Order::where('user_notify_num', '<=', 3)->where('status', '<>', 1)
                ->where('user_status', 'error')
                ->where(function ($query) use ($overtime) {
                    $query->where('user_notify_at', '<=', Carbon::now()->addSeconds($overtime))->orWhere('user_notify_at', null);
                })
                ->orderBy('updated_at')->first(['batch_no', 'amount', 'status', 'user_id', 'pay_at', 'notify_url', 'id', 'user_notify_num', 'order_id']);

            if ($data) {
                //
                $data = $data->toArray();
                Log::channel('user_notify')->info('>>>>>>>>定时回调收款订单开始>>>>>>>>');
                // 用户回调数据
                $getData = [
                    'order_id' => $data['order_id'],
                    'batch_no' => $data['batch_no'],
                    'amount' => $data['amount'],
                    'status' => $data['status'],
                    'agent_id' => $data['user_id'],
                    'pay_at' => $data['pay_at'],
                ];

                // 用户回调地址
                $url = $data['notify_url'];
                $id = $data['id'];
                $num = $data['user_notify_num'];
                Log::channel('user_notify')->info('处理数据', [$getData, $url, $id, $num]);
                // 商户key
                $key = User::where('agent_id', $data['user_id'])->value('key');
                $aa = NotifyController::orderNotify($getData, $id, $url, $num, $key);
                Log::channel('user_notify')->info('客户响应回调', [$aa]);
                Log::channel('user_notify')->info('<<<<<<<<定时回调收款订单结束<<<<<<<<');
            } else {
                // 没有核实订单就睡眠10秒
                sleep(5);
            }
        } catch (\Throwable $th) {
            //throw $th;
            Log::channel('user_notify')->info($th->getMessage());
        }
    }
}
