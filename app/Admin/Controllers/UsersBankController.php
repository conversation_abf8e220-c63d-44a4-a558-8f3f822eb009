<?php

namespace App\Admin\Controllers;

use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use App\Models\UsersBank;
use App\Http\Controllers\HuiFuController;
use App\Models\PayChannel;
use Dcat\Admin\Http\Controllers\AdminController;

class UsersBankController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UsersBank(), function (Grid $grid) {
            $grid->model()->orderByDesc('id');
            $grid->column('id')->sortable();
            $grid->column('user_id')->using(User::getUserName());
            $grid->column('num');
            $grid->column('name');
            $grid->column('idcard');
            $grid->column('status')->using([1 => '未认证', 2 => '已认证', 3 => '认证失败'])->dot([
                1 => '#fccb16',
                2 => 'success',
                3 => '#ee2746',
            ]);
            $grid->column('attestation')->using([1 => '未绑定', 2 => '已绑定', 3 => '绑定失败'])->dot([
                1 => '#fccb16',
                2 => 'success',
                3 => '#ee2746',
            ]);
            $grid->column('created_at');
            // $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->equal('user_id')->select(User::getUserName());
                $filter->equal('num');
            });

            // $grid->disableEditButton();
            $grid->disableViewButton();
            // $grid->disableCreateButton();

            // $grid->tools('<a href="error/bank" target=_blank><div class="btn btn-primary"><i class="fa fa-fw fa-user-secret"></i> 被占用银行卡</div></a>&nbsp;');
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UsersBank(), function (Show $show) {
            $show->field('id');
            $show->field('agent_id');
            $show->field('num');
            $show->field('name');
            $show->field('idcard');
            $show->field('city');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UsersBank(), function (Form $form) {
            $form->display('id');
            $form->select('user_id')->options(User::pluck('name', 'agent_id'))->required();
            $form->text('num')->required();
            $form->text('name')->required();
            $form->text('idcard')->required();
            $form->text('mobile_no')->help('随机生成，可不填写');
            $form->text('cert_begin_date')->help('随机生成，可不填写');

            $form->radio('status')->options([1 => '未认证', 2 => '已认证', 3 => '认证失败'])->default(1);
            $form->radio('attestation')->options([1 => '未绑定', 2 => '已绑定', 3 => '绑定失败'])->default(1);
            $form->textarea('msg');

            $form->display('created_at');
            $form->display('updated_at');
        })->saving(function (Form $form) {
            // 
            if (!$form->mobile_no) {
                $form->mobile_no = generateRandomPhoneNumber();
            }

            if (!$form->cert_begin_date) {
                $form->cert_begin_date = IdCardRandomDate($form->idcard);
            }
        })->saved(function (Form $form) {
            // 
            // 也可以这样获取自增ID
            $bank_id = $form->getKey();
            $pay_channel_id = PayChannel::where('channel_type', 1)->value('id');
            $UsersBank = UsersBank::where([
                'id' => $bank_id,
            ])->first();
            // 个人用户基本信息开户
            $data = (new HuiFuController())->userBasicDataIndv($bank_id, $pay_channel_id);
            $status = $data['status'];
            $msg = $data['msg'];
            $UsersBank->status = $status;

            // 更新用户银行信息状态是否满足三要素 第一步
            if ($status == 2) {
                $bb_bank_id = $data['bb_bank_id'];
                // 用户业务入驻 第二步
                $userBusiOpen = (new HuiFuController())->userBusiOpen($bb_bank_id);
                if ($userBusiOpen['status'] == 2) {
                    // 成功
                    $UsersBank->attestation = 2;
                } else {
                    // 失败
                    $UsersBank->attestation = 3;
                }
                $msg .= '---' . $userBusiOpen['msg'];
            }
            $UsersBank->msg = $msg;
            $UsersBank->save();
        });
    }
}
