<?php
/*
 * @Author: your name
 * @Date: 2021-03-04 10:17:27
 * @LastEditTime: 2021-04-22 10:34:05
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \huoke\app\Http\Controllers\Api\AuthController.php
 */

namespace App\Http\Controllers;


use Carbon\Carbon;
use App\Models\Order;
use App\Models\PayChannel;
use App\Http\Controllers\BaseController;
use App\Http\Controllers\HuiFuController;
use App\Http\Controllers\alipay\AliPayController;

class PayController extends BaseController
{
    /**
     * 支付宝轮训支付
     * 多通道支付宝轮训支付
     * @param mixed $arr
     * @return array
     */
    public function NativePay($arr = [], $channel): array
    {
        $data = PayChannel::whereIn('id', $arr['pay_channel_id_arr'])
            ->whereIn('channel_type', [PayChannel::HUIFU_TYPE, PayChannel::ALIPAY_TYPE])
            ->where('status', 1)
            ->where('order_status', 1)
            ->inRandomOrder()
            ->get();

        foreach ($data as $key => $value) {
            $arr['dataJson'] = $value->dataJson;
            $arr['id'] = $value->id;
            $arr['amount_limit'] = $value->amount_limit;
            $arr['name'] = $value->name;

            // 防止通道在限制的时间内重复发起支付请求
            $exists = Order::where('user_id', $arr['agent_id'])
                ->where('pay_channel_id', $value->id)
                ->where('created_at', '>', Carbon::now()->subSeconds($value->limit_time))
                ->exists();
            if (!$exists) {

                if ($value->channel_type == PayChannel::HUIFU_TYPE) {
                    $datas = (new HuiFuController())->payment($arr, $channel);
                } else if ($value->channel_type == PayChannel::ALIPAY_TYPE) {
                    $datas = (new AliPayController())->payment($arr, $channel);
                }
                if ($datas['code'] == 200) {
                    return $datas;
                }
            }
        }
        return [
            "code" => 400,
        ];
    }
}
