<?php

namespace App\Admin\Actions\Forms\DataExcel;

use App\Models\HFB;
use App\Models\User;
use Dcat\Admin\Admin;
use App\Models\PayChannel;
use App\Models\RechargeFail;
use Dcat\Admin\Widgets\Form;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use App\Admin\Actions\Imports\RechargeImport;

class RechargeForm extends Form
{
    public function handle(array $input)
    {
        try {
            //上传文件位置，这里默认是在storage中，如有修改请对应替换
            $file = storage_path('app/public/' . $input['file']);

            $agent_id = User::where('id', Admin::user()->user_id)->value('agent_id');
            if (!$agent_id) {
                return $this->response()->error('请先绑定商户id');
            }

            // 用户可用余额
            $recharge_amount = User::availableAmt($agent_id);
            $batch_amt = RechargeFail::where([
                'user_id' => $agent_id,
                'status' => 1,
            ])->sum('batch_amt');
            $recharge_amount = bcsub($recharge_amount, $batch_amt, 2);

            try {
                $objRead = IOFactory::createReader('Xlsx');
                $objSpreadsheet = $objRead->load($file);
                $objWorksheet = $objSpreadsheet->getSheet(0);
                $data = $objWorksheet->toArray();
                unset($data[0]);

                $coin = 0;
                foreach ($data as $k => $v) {
                    // 当前行
                    $count = $k + 1;

                    if (empty($v[0]) && empty($v[1]) && empty($v[2]) && empty($v[3]) && empty($v[4])) {
                        //
                        unset($data[$k]);
                        continue;
                    } else {
                        if (empty($v[1]) || empty($v[2]) || empty($v[3]) || empty($v[4])) {
                            //
                            return $this->response()->error('参数不完整，请补全第' . $count . '行的数据！');
                        }
                    }
                    $coin += (float) abs($v[1]);

                    // 没有订单就生成订单
                    if (empty($v[0]) && !empty($v[1]) && !empty($v[2]) && !empty($v[3]) && !empty($v[4])) {
                        //
                        $data[$k][0] = generateOrderNumber();
                    }
                    // 预留手续费，保证每笔下发成功
                    $recharge_amount = bcsub($recharge_amount, 1, 2);

                }

                if (count($data) > 200) {
                    //
                    return $this->response()->error('请上传200以内的数据');
                }

                if ($recharge_amount < $coin) {
                    //
                    return $this->response()->error('余额不足，文件中代付额度大于商户可用余额。');
                }
            } catch (\Exception $e) {
                return $this->response()->error('文件参数问题，请按照文件要求来编写。');
            }

            $notify_url = $input['notify_url'];
            Excel::import(new RechargeImport($data, $agent_id, $notify_url), $file);
            return $this->response()->success('数据导入成功')->refresh();
        } catch (\Exception $e) {
            return $this->response()->error($e->getMessage());
        }
    }


    // 上传表单
    public function form()
    {
        $this->html('<h4><font color="red" style="font-weight:bold">一旦提交，订单将不能撤回，请自行仔细核对订单信息，如有输错选错等情况造成的损失，后果自负！</font></h4>');
        $this->file('file', '上传数据（Excel）')->accept('xlsx')->autoUpload()->retainable()->threads(5)->accept('xlsx,xls')->rules('required', ['required' => '文件不能为空'])->help('请上传 .xlsx 结尾的文件，<a href="/storage/payModel.xlsx" target="_blank"> 点击下载导入模板 </a>');
        $this->url('notify_url', '回调地址')->default(env('APP_URL') . '/api/user')->help('有自己的回调地址请编辑，没有地址请勿编辑')->required();
    }
}
