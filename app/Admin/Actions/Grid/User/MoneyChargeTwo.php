<?php
/*
 * 下游商户充值
 */

namespace App\Admin\Actions\Grid\User;

use Dcat\Admin\Admin;
use Dcat\Admin\Grid\RowAction;
use App\Admin\Actions\Forms\User\MoneyChargeTwo as Form;

class MoneyChargeTwo extends RowAction
{
    public function render()
    {
        $id = "charge-moneysss-{$this->getKey()}";
        $recharge_point = 0;
        // $recharge_point = $this->row->recharge_point;

        // 模态窗
        $this->modal($id);

        return <<<HTML
<span class="grid-expand" data-toggle="modal" data-target="#{$id}" onclick=compute({$this->getKey()},{$recharge_point})>
   <a href="javascript:void(0)"><i class="fa fa-money"></i> 余额上分
</a>
</span>

<script>
        function compute(id,point) {
            var money = $('#money-'+id);
            money.blur(function(e) {
                let val =money.val();
                let zeo = 0;

                let money1 = 0;
                let money2 = 0;

                if(val > zeo){
                    //
                    money2 = (val*point/100).toFixed(2);
                    mon = 0;
                    if(money2 < mon){
                        money2 = mon;
                    }
                    money1 = (val - money2).toFixed(2);

                }else{
                    money1 = val;
                    money2 = 0;

                }

                $('#money1-'+id).val(money1)
                $('#money2-'+id).val(money2)
            })
        }
</script>
HTML;
    }

    protected function modal($id)
    {
        // 在弹窗标题处显示当前行的用户名
        // $name = $this->row->name;
        $idss = $this->row->id;
        // 充值点位
        $recharge_point = $this->row->recharge_point;
        // 供应商
        $pay_channel_id = $this->row->pay_channel_id;

        // 工具表单
        $form = new Form($idss, $recharge_point, $pay_channel_id);

        // 刷新页面时移除模态窗遮罩层
        // 从 v1.5.0 版本开始可以移除这段 JS 代码
        Admin::script('Dcat.onPjaxComplete(function () {
            $(".modal-backdrop").remove();
            $("body").removeClass("modal-open");
        }, true)');

        // 通过 Admin::html 方法设置模态窗HTML
        Admin::html(
            <<<HTML
<div class="modal fade" id="{$id}">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">余额上分</h4>
         <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
      </div>
      <div class="modal-body">
        {$form->render()}
      </div>
    </div>
  </div>
</div>
HTML
        );
    }
}
