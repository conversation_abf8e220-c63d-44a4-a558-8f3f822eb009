<?php

namespace App\Admin\Controllers;

use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Admin;
use App\Models\Recharge;
use App\Models\RechargeList;
use App\Admin\Actions\Show\RechargeError;
use App\Admin\Actions\Show\RechargeSuccess;
use App\Admin\Actions\Show\RechargeNotifyUrl;
use App\Admin\Actions\Grid\Excel\RechargeModal;
use App\Admin\Actions\Show\PayTransferGetProof;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Admin\Actions\Grid\Channel\RechargeOrder;
use App\Admin\Actions\Grid\Recharge\RechargeListTable;

class RechargeController extends AdminController
{
    protected $status_arr;

    public function __construct()
    {
        $this->status_arr = [1 => '未支付', 2 => '提现成功', 3 => '失败', 4 => '部分成功'];
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        // 虚拟部
        if (Admin::user()->isRole('agent1')) {
            //
            return $this->gridAgent();
        }

        return Grid::make((new Recharge())->with('user')->with('bank'), function (Grid $grid) {

            $startE = request()->input('created_at');
            $grid->model()->when(!$startE, function ($query) {
                $query->whereBetween('created_at', [date('Y-m-d 00:00:00'), 'end' => date('Y-m-d H:i:s')]);
            })->orderByDesc('id');
            $grid->tools(function (Grid\Tools $tools) {
                $tools->append(new RechargeOrder());
            });
            $grid->column('id')->sortable();
            $grid->column('batch_no');
            $grid->column('order_id');
            $grid->column('user_id')->setAttributes(['style' => 'color:#2d9e2bd6']);
            $grid->column('user.name', '商户名称');
            $grid->column('batch_amt');
            $grid->column('batch_rate_amt', '手续费');
            $grid->column('batch_amt_o', '提现成功金额')->display(function () {
                return RechargeList::where('recharge_id', $this->id)->where('status', 2)->sum('amount');
            });
            $grid->column('recharge_list', '子订单')->display(function () {
                $id = $this->id;
                $str = '';
                $count = RechargeList::where('recharge_id', $id)->where('qx_status', 2)->count();
                if ($count) {
                    $str = "成功({$count})";
                }
                $qx_status_1 = RechargeList::where('recharge_id', $id)->where('qx_status', 1)->count();
                if ($qx_status_1) {
                    $str .= "未取现({$qx_status_1})";
                }
                $qx_status_3 = RechargeList::where('recharge_id', $id)->where('qx_status', 3)->count();
                if ($qx_status_3) {
                    $str .= "取现失败({$qx_status_3})";
                }
                $qx_status_4 = RechargeList::where('recharge_id', $id)->where('qx_status', 4)->count();
                if ($qx_status_4) {
                    $str .= "取现中({$qx_status_4})";
                }
                return $str;
            })->modal('子订单列表', RechargeListTable::make());

            $grid->column('bank.name', '收款人');
            $grid->column('bank.num', '收款卡');
            $grid->column('status')->using($this->status_arr)->dot([
                1 => '#fccb16',
                2 => 'success',
                3 => '#ee2746',
                4 => 'success'
            ]);
            $grid->column('error_msg');
            $grid->column('user_status')->help('用户响应  success 响应成功，其他的每隔 5分钟回调一次，共3次');
            $grid->column('user_notify_num');
            $grid->column('user_notify_at');
            // $grid->column('pay_at');
            $grid->column('created_at');
            // $grid->column('updated_at')->sortable();
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($actions->row->status != 1) {
                    $actions->append(RechargeNotifyUrl::make()->setKey($actions->row->id));
                }
                if ($actions->row->status == 1) {
                    $actions->append(RechargeError::make()->setKey($actions->row->id));
                    $actions->append(RechargeSuccess::make()->setKey($actions->row->id));
                }
                if ($actions->row->status == 2) {
                    //
                    $actions->append(PayTransferGetProof::make()->setKey($actions->row->id));
                }
            });

            $grid->filter(function (Grid\Filter $filter) {
                // 展开过滤器
                $filter->expand();
                // 更改为 panel 布局
                $filter->panel();

                $filter->equal('id')->width(3);
                $filter->equal('batch_no')->width(3);
                $filter->equal('order_id')->width(3);
                $filter->equal('user_id')->width(3);
                $filter->equal('status')->select($this->status_arr)->width(3);
                $filter->equal('user_status')->width(3);
                $filter->equal('bank.name', '收款人')->width(3);
                $filter->equal('bank.num', '收款卡')->width(3);
                $filter->between('created_at')->datetime()->default([
                    'start' => date('Y-m-d 00:00:00'),
                    'end' => date('Y-m-d 23:59:59')
                ])->width(6);
            });

            $grid->disableCreateButton();
            $grid->disableViewButton();
            $grid->disableEditButton();
            $grid->enableDialogCreate();
            // 显示快捷编辑按钮
            $grid->showQuickEditButton();
            // 设置弹窗宽高，默认值为 '700px', '670px'
            $grid->setDialogFormDimensions('50%', '80%');

            // 开启字段选择器功能
            $grid->showColumnSelector();
            // 设置默认隐藏字段
            $grid->hideColumns(['error_msg', 'user_status', 'user_notify_num', 'user_notify_at']);
        });
    }

    protected function gridAgent()
    {
        return Grid::make((new Recharge())->with('user')->with('bank'), function (Grid $grid) {
            $user_id = Admin::user()->user_id;
            $agent_id = User::where('id', $user_id)->value('agent_id');
            $startE = request()->input('created_at');
            $grid->model()->when(!$startE, function ($query) {
                $query->whereBetween('created_at', [date('Y-m-d 00:00:00'), 'end' => date('Y-m-d H:i:s')]);
            })->where('user_id', $agent_id)->orderByDesc('id');

            $grid->column('id')->sortable();
            $grid->column('batch_no');
            $grid->column('order_id');
            $grid->column('user_id')->setAttributes(['style' => 'color:#2d9e2bd6']);
            $grid->column('user.name', '商户名称');
            $grid->column('batch_amt');
            $grid->column('batch_rate_amt', '手续费');
            $grid->column('batch_amt_o', '提现成功金额')->display(function () {
                return RechargeList::where('recharge_id', $this->id)->where('status', 2)->sum('amount');
            });
            $grid->column('recharge_list', '子订单')->display(function () {
                $id = $this->id;
                $str = '';
                $count = RechargeList::where('recharge_id', $id)->where('qx_status', 2)->count();
                if ($count) {
                    $str = "成功({$count})";
                }
                $qx_status_1 = RechargeList::where('recharge_id', $id)->where('qx_status', 1)->count();
                if ($qx_status_1) {
                    $str .= "未取现({$qx_status_1})";
                }
                $qx_status_3 = RechargeList::where('recharge_id', $id)->where('qx_status', 3)->count();
                if ($qx_status_3) {
                    $str .= "取现失败({$qx_status_3})";
                }
                $qx_status_4 = RechargeList::where('recharge_id', $id)->where('qx_status', 4)->count();
                if ($qx_status_4) {
                    $str .= "取现中({$qx_status_4})";
                }
                return $str;
            })->modal('子订单列表', RechargeListTable::make());

            $grid->column('bank.name', '收款人');
            $grid->column('bank.num', '收款卡');
            $grid->column('status')->using($this->status_arr)->dot([
                1 => '#fccb16',
                2 => 'success',
                3 => '#ee2746',
                4 => 'success'
            ]);
            $grid->column('error_msg');
            // $grid->column('user_status')->help('用户响应  success 响应成功，其他的每隔 5分钟回调一次，共3次');
            // $grid->column('user_notify_num');
            // $grid->column('user_notify_at');
            // $grid->column('pay_at');
            $grid->column('created_at');
            // $grid->column('updated_at')->sortable();
            $grid->filter(function (Grid\Filter $filter) {
                // 展开过滤器
                $filter->expand();
                // 更改为 panel 布局
                $filter->panel();

                // $filter->equal('id')->width(3);
                $filter->equal('batch_no')->width(3);
                $filter->equal('order_id')->width(3);
                // $filter->equal('user_id')->width(3);
                $filter->equal('status')->select($this->status_arr)->width(3);
                // $filter->equal('user_status')->width(3);
                $filter->equal('bank.name', '收款人')->width(3);
                $filter->equal('bank.num', '收款卡')->width(3);
                $filter->between('created_at')->datetime()->default([
                    'start' => date('Y-m-d 00:00:00'),
                    'end' => date('Y-m-d 23:59:59')
                ])->width(6);
            });
            $grid->disableDeleteButton();
            $grid->disableCreateButton();
            $grid->disableViewButton();
            $grid->disableEditButton();
            $grid->enableDialogCreate();
            $grid->disableRowSelector();
            // 显示快捷编辑按钮
            // $grid->showQuickEditButton();
            // 设置弹窗宽高，默认值为 '700px', '670px'
            $grid->setDialogFormDimensions('50%', '80%');

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($actions->row->status != 1) {
                    $actions->append(RechargeNotifyUrl::make()->setKey($actions->row->id));
                }
                if ($actions->row->status == 2) {
                    //
                    $actions->append(PayTransferGetProof::make()->setKey($actions->row->id));
                }
            });

            // 开启字段选择器功能
            $grid->showColumnSelector();
            // 设置默认隐藏字段
            $grid->hideColumns(['error_msg']);
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Recharge(), function (Show $show) {
            $show->field('id');
            $show->field('batch_no');
            $show->field('order_id');
            $show->field('user_id');
            $show->field('batch_amt');
            $show->field('notify_url');
            $show->field('pay_at');
            $show->field('desc');
            $show->field('status');
            $show->field('user_notify_at');
            $show->field('user_notify_num');
            $show->field('user_ip');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Recharge(), function (Form $form) {
            $form->display('id');
            $form->text('batch_no');
            $form->text('order_id');
            $form->text('user_id');
            $form->text('batch_amt');
            $form->text('batch_rate_amt', '手续费');
            // $form->text('notify_url');
            $form->datetime('pay_at');
            $form->radio('status')->options($this->status_arr);
            $form->datetime('user_notify_at');
            $form->number('user_notify_num');
            $form->text('user_status');
            $form->textarea('notify_url');
            $form->ip('user_ip');
            $form->textarea('error_msg');
            $form->text('desc');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
