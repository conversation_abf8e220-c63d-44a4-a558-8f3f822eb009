<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Kq extends Model
{
    /**
     * 随机获取一个银行的key
     * @return mixed
     */
    static public function getRandomBankKey()
    {
        // 
        $bankMap = self::getKqBankMap();
        $keys = array_keys($bankMap);
        return $keys[array_rand($keys)];
    }

    static public function getKqBankMap()
    {
        $bankMap = [
            'ICBC' => '工商银行',
            'ABC' => '农业银行',
            'BOC' => '中国银行',
            'CCB' => '建设银行',
            'CMB' => '招商银行',
            'SPDB' => '浦发银行',
            'CITIC' => '中信银行',
            'CIB' => '兴业银行',
            'CMBC' => '民生银行',
            'PAB' => '平安银行',
            'SHB' => '上海银行',
            'CEB' => '光大银行',
            'HXB' => '华夏银行',
            'PSBC' => '中国邮政储蓄银行',
            'GDB' => '广发银行',
            'BCOM' => '交通银行',
            // 'JSB' => '江苏银行',
            // 'NBCB' => '宁波银行',
            // 'DLB' => '大连银行',
            // 'WZB' => '温州银行',
            // 'JJB' => '九江银行',
            // 'NCB' => '南昌银行',
            // 'BSB' => '包商银行',
            // 'JZB' => '锦州银行',
            // 'QLB' => '齐鲁银行',
            // 'WFB' => '潍坊银行',
            // 'HBBB' => '湖北银行',
            // 'CDB' => '承德银行',
            // 'HZB' => '杭州银行',
            // 'TZB' => '台州银行',
            // 'LZB' => '兰州银行',
            // 'LZCCB' => '柳州银行',
            // 'SRB' => '上饶银行',
            // 'LJB' => '龙江银行',
            // 'GZCB' => '广州银行',
            // 'HSB' => '徽商银行',
            // 'NXB' => '宁夏银行',
            // 'DYB' => '东营银行',
            // 'NJCB' => '南京银行',
            // 'GYB' => '贵阳银行',
            // 'QHB' => '青海银行',
            // 'YZB' => '鄞州银行',
            // 'CSB' => '长沙银行',
            // 'CQB' => '重庆银行',
            // 'HBB' => '河北银行',
            // 'CDRCBB' => '成都农商',
            // 'ZJTLCB' => '泰隆商行',
            // 'SXRCB' => '绍兴银行',
            // 'ZJCZCB' => '稠州商行',
            // 'ZJRCC' => '浙江农信',
            // 'HZRCB' => '湖州商行',
            // 'ZJMTCB' => '民泰商行',
            // 'JHRCB' => '金华银行',
            // 'GZRCB' => '赣州银行',
            // 'DGB' => '东莞银行',
            // 'HYB' => '华润银行',
            // 'NYB' => '南粤银行',
            // 'SJB' => '盛京银行',
            // 'LSSRCB' => '临商银行',
            // 'DZB' => '德州银行',
            // 'LSB' => '莱商银行',
            // 'RZRCB' => '日照银行',
            // 'QSRCB' => '齐商银行',
            // 'JSRCU' => '江苏农信',
            // 'FJRCC' => '福建农信',
            // 'QHRCC' => '青海农信',
            // 'HNRCC' => '湖南农信',
            // 'QDRCB' => '青岛银行',
            // 'SQB' => '商丘银行',
            // 'HKB' => '汉口银行',
            // 'JLRCB' => '吉林银行',
            // 'CQRCB' => '重庆农商',
            // 'TJRCB' => '天津农商',
            // 'TJB' => '天津银行',
            // 'YNRCC' => '云南农信',
            // 'FDB' => '富滇银行',
            // 'SXRCC' => '山西省联社',
            // 'BOCD' => '成都银行',
            // 'CBZZ' => '郑州银行',
            // 'BOWH' => '乌海银行',
            // 'BOSZ' => '苏州银行',
            // 'SZRB' => '深圳农商行',
            // 'QZCCB' => '泉州银行',
            // 'BOLY' => '洛阳银行',
            // 'KLB' => '昆仑银行',
            // 'JSHB' => '晋商银行',
            // 'JXCCB' => '嘉兴银行',
            // 'JNBANK' => '济宁银行',
            // 'YRRCB' => '黄河农商行',
            // 'HDCB' => '邯郸银行',
            // 'FUXINBANK' => '阜新银行',
            // 'BOFS' => '抚顺银行',
            // 'CYCB' => '朝阳银行',
            // 'BANKOFAS' => '鞍山银行',
            // 'HZCCB' => '湖州银行',
            // 'BOB' => '北京银行',
            // 'HNRCU' => '海南农信社',
            // 'GXBBWB' => '广西北部湾',
            // 'WXRCB' => '无锡农商行',
            // 'WJRCB' => '吴江农商行',
            // 'BJRCB' => '北京农商行',
            // 'CSRCB' => '常熟农商行',
            // 'JYRCB' => '江阴农商行',
            // 'SDRCB' => '顺德农商行',
            // 'SZSB' => '石嘴山银行',
            // 'DGRCB' => '东莞农商行',
            // 'CRB' => '广州农商行',
            // 'HRBB' => '哈尔滨银行',
            // 'IMB' => '内蒙古银行',
            // 'SRCB' => '上海农商行',
            // 'GHB' => '广东华兴银行',
            // 'FJHXB' => '福建海峡银行',
            // 'ORDOSB' => '鄂尔多斯银行',
            // 'ZJRB' => '张家港农商行',
            // 'BOHH' => '新疆汇和银行',
            // 'WHCCB' => '威海市商业银行',
            // 'CJCCB' => '江苏长江商业银行',
        ];
        ksort($bankMap);
        return $bankMap;
    }

    static public function getKqBankAgreementMap($bank_type)
    {
        $agreement = [
            'ICBC' => 'https://www.99bill.com/seashell/html/agreement/bankicbcmobile.html?needBack=true',
            'ABC' => 'https://www.99bill.com/seashell/webapp/agreement/bankh5abc.html?needBack=true',
            'BOC' => 'https://www.99bill.com/seashell/webapp/billtrunk/agreement/kq_boc_credit_fw.html?needBack=true',
            'CCB' => 'https://www.99bill.com/seashell/webapp/billtrunk/agreement/kq_ccb_fw.html?needBack=true',
            'CMB' => 'https://www.99bill.com/seashell/webapp/billtrunk/agreement/kq_cmb_fw.html?needBack=true',
            'SPDB' => 'https://www.99bill.com/seashell/webapp/agreement/bankh5spdb.html?needBack=true',
            'CITIC' => 'https://www.99bill.com/seashell/html/agreement/bankciticweb.html?needBack=true',
            'CIB' => 'https://www.99bill.com/seashell/webapp/agreement/bankh5cib.html?needBack=true',
            'CMBC' => 'https://www.99bill.com/seashell/webapp/agreement/bankh5cmbc-credit.html?needBack=true',
            'PAB' => 'https://www.99bill.com/seashell/webapp/agreement/bankh5pab.html?needBack=true',
            'SHB' => 'https://www.99bill.com/seashell/webapp/agreement/bankh5shb.html?needBack=true',
            'CEB' => 'https://www.99bill.com/seashell/webapp/agreement/bankh5ceb.html?needBack=true',
            'HXB' => 'https://www.99bill.com/seashell/html/agreement/oneclickagreementhxmobile.html?needBack=true',
            'PSBC' => 'https://www.99bill.com/seashell/html/agreement/bankpsbccreditweb.html?needBack=true',
        ];

        return isset($agreement[$bank_type]) ? $agreement[$bank_type] : 'https://www.99bill.com/seashell/html/agreement/oneclickagreement.html';
    }

    static public function getKqBankIdMap($bank_type)
    {
        return isset(self::getKqBankMap()[$bank_type]) ? self::getKqBankMap()[$bank_type] : '未知';
    }
}

