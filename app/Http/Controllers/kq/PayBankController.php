<?php
namespace App\Http\Controllers\kq;

use Exception;
use App\Models\PayChannel;
use remote\RemoteProcessor;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\BaseController;

/**
 * 下发 转账给银行
 */

class PayBankController extends BaseController
{
    /**
     * 单笔付款到银行
     * @param mixed $arr
     * @throws \Exception
     * @return array
     */
    public function C1017($arr = [])
    {
        global $config;
        try {
            $config = require(APP_PATH . '/config/MerchantConfig.php');
            $log = 'kq_log';
            Log::channel($log)->info('------------------------------------------------------------');
            Log::channel($log)->info('C1017接口接收到前端数据：', [$arr]);
            $dataJson = PayChannel::dataJson($arr['pay_channel_id']);
            $config = array_merge($config, $dataJson);

            //接收form表单提交过来的数据并处理
            $request_Head_Array = array();
            $request_Body_Array = array();
            //1.组装明文head数组
            $request_Head_Array['version'] = "1.0.0";
            $request_Head_Array['messageType'] = "C1017";
            $request_Head_Array['memberCode'] = $config['account']['memberCode'];
            $request_Head_Array['externalRefNumber'] = $arr['externalRefNumber'];
            //2.组装明文body数组
            $request_Body_Array['amount'] = $arr['amount'] * 100;
            $request_Body_Array['cardHolderName'] = $arr['cardHolderName'];
            $request_Body_Array['bankName'] = $arr['bankName'];
            $request_Body_Array['pan'] = $arr['pan'];
            $request_Body_Array['reMark'] = "交易成功";
            $request_Body_Array['notifyUrl'] = rtrim(env('KQ_URL'), '/') . '/api/recharge/notice/kq';

            $responseDecryptMessage = RemoteProcessor::requestKQ($request_Head_Array, $request_Body_Array, $config);
            Log::channel($log)->info('C1017接口收到快钱返回明文数据：' . $responseDecryptMessage);
            Log::channel($log)->info('------------------------------------------------------------');

            $data = json_decode($responseDecryptMessage, true);

            if (!$data) {
                throw new Exception('配置错误');
            }
            $bizResponseCode = $data['responseBody']['bizResponseCode'];
            $bizResponseMessage = $data['responseBody']['bizResponseMessage'];
            if (!in_array($bizResponseCode, array('0000', 'UMGW00086'))) {
                throw new Exception($bizResponseMessage);
            }
            return [
                "resp_code" => "200",
                "resp_desc" => $bizResponseMessage
            ];

        } catch (\Throwable $th) {
            //throw $th;
            return [
                "resp_code" => "400",
                "resp_desc" => $th->getMessage(),
            ];
        }
    }

    /**
     * 批量付款到银行
     * @param mixed $arr
     * @throws \Exception
     * @return array
     */
    public function C1019($arr = [])
    {
        global $config;
        try {
            $config = require(APP_PATH . '/config/MerchantConfig.php');
            $log = 'kq_log';
            Log::channel($log)->info('------------------------------------------------------------');
            Log::channel($log)->info('C1019接口接收到前端数据：', [$arr]);
            $dataJson = PayChannel::dataJson($arr['pay_channel_id']);
            $config = array_merge($config, $dataJson);

            //接收form表单提交过来的数据并处理
            $request_Head_Array = array();
            $request_Body_Array = array();
            //1.组装明文head数组
            $request_Head_Array['version'] = "1.0.0";
            $request_Head_Array['messageType'] = "C1019";
            $request_Head_Array['memberCode'] = $config['account']['memberCode'];
            $request_Head_Array['batchId'] = $arr['externalRefNumber'];
            //2.组装明文body数组

            $request_Body_Array['requestTime'] = date('YmdHis');
            $request_Body_Array['memberName'] = $config['account']['memberName'];
            $request_Body_Array['amountTotal'] = $arr['amount'] * 100;
            $request_Body_Array['numTotal'] = '1';
            $request_Body_Array['feePayer'] = '1';
            $request_Body_Array['cur'] = 'CNY';
            $request_Body_Array['checkAmtCnt'] = '0';
            $request_Body_Array['batchFail'] = '0';
            $request_Body_Array['autoRefund'] = '0';
            $request_Body_Array['phoneNoteFlag'] = '1';
            $request_Body_Array['partnerWithdraw'] = '1';
            // $request_Body_Array['merchantMemo1'] = $frontParam['merchantMemo1'];
            // $request_Body_Array['merchantMemo2'] = $frontParam['merchantMemo2'];
            // $request_Body_Array['merchantMemo3'] = $frontParam['merchantMemo3'];
            // 请确保提供的 notifyUrl 地址公网可访问
            $request_Body_Array['notifyUrl'] = rtrim(env('KQ_URL'), '/') . '/api/recharge/notice/kq';
            $getBankByBin = getBankByBin($arr['pan']);
            $bankName = isset($getBankByBin['bankname']) ? $getBankByBin['bankname'] : $arr['bankName'];
            $province = isset($getBankByBin['province']) ? $getBankByBin['province'] : '北京';
            $city = isset($getBankByBin['city']) ? $getBankByBin['city'] : '北京';
            $txnList = [
                [
                    'externalRefNumber' => $arr['externalRefNumber'],
                    'amount' => $arr['amount'] * 100,
                    'bankName' => $bankName,
                    'cardHolderName' => $arr['cardHolderName'],
                    'pan' => $arr['pan'],
                    'payeeType' => 1,
                    'province' => $province,
                    'city' => $city,
                    'reMark' => "交易成功",
                ]
            ];

            $request_Body_Array['txnList'] = RemoteProcessor::compress($txnList);
            Log::channel($log)->info('C1019接口发送快钱返回明文数据：', [$txnList]);
            $responseDecryptMessage = RemoteProcessor::requestKQ($request_Head_Array, $request_Body_Array, $config);
            Log::channel($log)->info('C1019接口收到快钱返回明文数据：' . $responseDecryptMessage);
            Log::channel($log)->info('------------------------------------------------------------');

            $data = json_decode($responseDecryptMessage, true);

            if (!$data) {
                throw new Exception('配置错误');
            }
            $bizResponseCode = $data['responseBody']['bizResponseCode'];
            $bizResponseMessage = $data['responseBody']['bizResponseMessage'];
            $txnList = $data['responseBody']['txnList'];
            if (!in_array($bizResponseCode, array('0000', 'UMGW00086'))) {
                throw new Exception($bizResponseMessage);
            }
            $txnList = RemoteProcessor::uncompress($txnList);
            if (is_array($txnList)) {
                $txnStatus = $txnList[0]['txnStatus'];
                $bizResponseMessage = $txnList[0]['bizResponseMessage'] ?? '交易失败';
                if (in_array($txnStatus, ['S', 'P'])) {
                    return [
                        "resp_code" => "200",
                        "resp_desc" => $bizResponseMessage
                    ];
                }
                throw new Exception($bizResponseMessage);
            }
            throw new Exception('解密错误');
        } catch (\Throwable $th) {
            //throw $th;
            return [
                "resp_code" => "400",
                "resp_desc" => $th->getMessage(),
            ];
        }
    }
}