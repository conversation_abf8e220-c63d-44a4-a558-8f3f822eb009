<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use App\Models\User;
use Dcat\Admin\Admin;
use App\Models\Recharge;
use App\Models\UserAccountChangeRecord;
use Dcat\Admin\Http\Controllers\AdminController;

class UserAccountChangeRecordController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        if (Admin::user()->isRole('agent')) {
            $user_id = User::where('id', Admin::user()->user_id)->value('agent_id');
        } else {
            $user_id = request()->get('user_id', '');
        }
        return Grid::make((new UserAccountChangeRecord())->where('user_id', $user_id)->with([
            'user' => function ($query) {
                //
                $query->select('agent_id', 'name');
            }
        ]), function (Grid $grid) use ($user_id) {
            $grid->model()->orderBydesc('id');
            $grid->column('id')->sortable();
            $grid->column('user_id');
            $grid->column('user.name', '商户名称');
            $grid->column('type')->using((new UserAccountChangeRecord())->type)->label((new UserAccountChangeRecord())->type_color);
            $grid->column('amount');
            $grid->column('service_charge');
            $grid->column('money_q');
            $grid->column('money_h');
            $grid->column('order_id');
            $grid->column('created_at')->sortable();
            // $grid->column('updated_at')->sortable();

            $grid->disableViewButton();

            $grid->filter(function (Grid\Filter $filter) use ($user_id) {
                $filter->equal('type')->select((new UserAccountChangeRecord())->type)->width(3);
                // $filter->equal('user_id')->width(3);
                if (Admin::user()->isRole('administrator')) {
                    $filter->equal('user_id')->select(User::withTrashed()->where('agent_id', $user_id)->pluck('name', 'agent_id')->toArray())->width(3);
                }
                // 设置datetime类型
                $filter->between('created_at')->datetime()->width(6);
                $filter->like('order_id')->width(3);

                // 展开过滤器
                $filter->expand();
                // 更改为 panel 布局
                $filter->panel();
            });

            if (Admin::user()->isRole('agent')) {
                $grid->disableCreateButton();
                $grid->disableDeleteButton();
                $grid->disableEditButton();
                $grid->disableRowSelector();
            }
            if (Admin::user()->isRole('administrator')) {
                $grid->header(function ($collection) use ($user_id) {
                    // $user_id = request()->get('user_id');
                    $type = request()->get('type');
                    $start = request()->get('created_at[start]');
                    $created_at = request()->get('created_at');

                    $data = UserAccountChangeRecord::when($user_id, function ($query, $user_id) {
                        $query->where('user_id', $user_id);
                    })->when($type, function ($query, $type) {
                        $query->where('type', $type);
                    })->when($created_at, function ($query, $created_at) {
                        $query->whereBetween('created_at', $created_at);
                    })->get()->toArray();

                    // 代付
                    $successCount1 = collect($data)->whereIn('type', [1])->count();
                    $successcoin1 = collect($data)->whereIn('type', [1])->sum('amount');
                    $successService1 = collect($data)->whereIn('type', [1])->sum('service_charge');

                    // 充值
                    $successCount = collect($data)->whereIn('type', [2])->count();
                    $successcoin = collect($data)->whereIn('type', [2])->sum('amount');
                    $successService = collect($data)->whereIn('type', [2])->sum('service_charge');

                    // 退款
                    $errorCount = collect($data)->whereIn('type', [3])->count();
                    $errorCoin = collect($data)->whereIn('type', [3])->sum('amount');
                    $sum = $successCount + $errorCount + $successCount1;
                    return "<div class='filter-box shadow-0 card mb-0'>共 $sum 笔&nbsp;|&nbsp;代付笔数：$successCount1&nbsp;|&nbsp;代付金额：$successcoin1&nbsp;|&nbsp;扣除手续费：$successService1&nbsp;|&nbsp;退款笔数：$errorCount&nbsp;|&nbsp;退款金额：$errorCoin&nbsp;|&nbsp;充值笔数：$successCount&nbsp;|&nbsp;充值金额：$successcoin&nbsp;|&nbsp;充值手续费：$successService&nbsp;</div>";
                });
            }
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UserAccountChangeRecord(), function (Show $show) {
            $show->field('id');
            $show->field('user_id');
            $show->field('type');
            $show->field('service_charge');
            $show->field('amount');
            $show->field('money_q');
            $show->field('money_h');
            $show->field('order_id');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UserAccountChangeRecord(), function (Form $form) {
            $form->display('id');
            $form->text('user_id');
            $form->select('type')->options((new UserAccountChangeRecord())->type);
            $form->text('service_charge');
            $form->text('amount');
            $form->text('money_q');
            $form->text('money_h');
            $form->text('order_id');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
