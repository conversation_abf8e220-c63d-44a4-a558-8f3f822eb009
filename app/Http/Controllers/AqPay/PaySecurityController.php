<?php

/**
 * 安全发
 * 登录地址：cyf.xzs-branch.com
 * 接口：http://**************:8080/
 * 接口文档：https://doc.apipost.net/docs/4504384e90e0000?locale=zh-cn
 * 密码：362298
 */
namespace App\Http\Controllers\AqPay;

use App\Http\Controllers\BaseController;

class PaySecurityController extends BaseController
{
    private const API_PAY_URL = 'http://**************:8080';
    private const APP_ID = 'APP-6144844118978304';
    private const APP_SECRET = 'GTpfoNEjoaL8vzNnjeBoqg3N62sp4Oxq';

    /**
     * Summary of TransferPlugin
     * @param mixed $arr
     * @return array
     */
    public function TransferPlugin($arr = [])
    {
        try {
            //code...
            $APP_ID = $arr['config']['app_id'] ?? '';
            $APP_SECRET = $arr['config']['APP_SECRET'] ?? '';

            $callbackUrl = rtrim(env('APP_URL'), '/') . '/api/recharge/notice/anquanpay';

            $requestBody = json_encode([
                'accountId' => $arr['config']['accountId'] ?? 'XXX',
                'orderType' => '支转支',
                'callbackUrl' => $callbackUrl,
                'payerShowName' => $arr['config']['payerShowName'] ?? 'XXX',
                'records' => [
                    [
                        'tradeId' => $arr['recharge_list_order'],
                        'receiverName' => $arr['cardHolderName'],
                        'receiverPaymentAccount' => $arr['pan'],
                        'amount' => $arr['amount'],
                        'transferRemark' => '',
                        'transferTitle' => '',
                    ]
                ]
            ]);

            $timestamp = (string) round(microtime(true) * 1000);
            $nonce = self::generateRandomString(16);

            $sign = self::calculateSign($APP_ID, $timestamp, $nonce, $requestBody, $APP_SECRET);

            $headers = [
                'Content-Type: application/json',
                'X-App-Id: ' . $APP_ID,
                'X-Timestamp: ' . $timestamp,
                'X-Nonce: ' . $nonce,
                'X-Sign: ' . $sign
            ];

            $response = self::sendRequest(
                $arr['config']['pay_url'] . '/axf/api/safeTradeInfo/add',
                // self::API_PAY_URL . '/axf/api/safeTradeInfo/add',
                $requestBody,
                $headers
            );

            if ($response['code'] == 200) {

                return [
                    'status' => 200,
                    'message' => '请求成功',
                ];
            }

            return [
                'status' => 400,
                'message' => $response['msg'],
            ];
        } catch (\Throwable $th) {
            //throw $th;
            return [
                'status' => 400,
                'message' => $th->getMessage(),
            ];
        }
        // "{"code":200,"msg":"请求成功","data":{"tradeIds":"[\"1926931423389892610\"]","batchNo":"BT202505261720262425-1"}}"
    }

    private static function generateRandomString($length = 16)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }

    private static function calculateSign($appId, $timestamp, $nonce, $body, $appSecret)
    {
        $content = $appId . $timestamp . $nonce . $body . $appSecret;
        return strtoupper(md5($content));
    }

    private static function sendRequest($url, $requestBody, $headers)
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $requestBody);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $response = curl_exec($ch);
        curl_close($ch);
        $response = json_decode($response, true);
        return $response;
    }

    public static function addBankTradeInfo()
    {
        // 1. 准备请求参数
        $requestBody = '{"accountId":"XXX","orderType":"支转支","callbackUrl":"","payerShowName":"XXX","records":[{"transferTitle":"","receiverName":"XXX","receiverPaymentAccount":"XXXX","amount":1,"transferRemark":"","accountType":2}]}';
        echo $requestBody . "\n";
        $timestamp = (string) round(microtime(true) * 1000);
        $nonce = self::generateRandomString(16);

        // 2. 计算签名
        $sign = self::calculateSign(self::APP_ID, $timestamp, $nonce, $requestBody, self::APP_SECRET);

        // 3. 设置请求头
        $headers = [
            'Content-Type: application/json',
            'X-App-Id: ' . self::APP_ID,
            'X-Timestamp: ' . $timestamp,
            'X-Nonce: ' . $nonce,
            'X-Sign: ' . $sign
        ];

        echo "请求头：" . json_encode($headers) . "\n";

        // 4. 发送请求
        $response = self::sendRequest(
            self::API_PAY_URL . '/axf/api/safeTradeInfo/add',
            $requestBody,
            $headers
        );
        echo $response . "\n";
    }

    public static function getTradeInfo()
    {
        $tradeId = "1908511351107198978";
        $requestBody = "";
        $timestamp = (string) round(microtime(true) * 1000);
        $nonce = self::generateRandomString(16);

        $sign = self::calculateSign(self::APP_ID, $timestamp, $nonce, $requestBody, self::APP_SECRET);

        $headers = [
            'Content-Type: application/json',
            'X-App-Id: ' . self::APP_ID,
            'X-Timestamp: ' . $timestamp,
            'X-Nonce: ' . $nonce,
            'X-Sign: ' . $sign
        ];

        $response = self::sendRequest(
            self::API_PAY_URL . '/axf/api/safeTradeInfo/query/' . $tradeId,
            $requestBody,
            $headers
        );
        echo $response . "\n";
    }

    public static function downloadTradeInfo()
    {
        $tradeId = "1907735142780112898";
        $requestBody = "";
        $timestamp = (string) round(microtime(true) * 1000);
        $nonce = self::generateRandomString(16);

        $sign = self::calculateSign(self::APP_ID, $timestamp, $nonce, $requestBody, self::APP_SECRET);

        $headers = [
            'Content-Type: application/json',
            'X-App-Id: ' . self::APP_ID,
            'X-Timestamp: ' . $timestamp,
            'X-Nonce: ' . $nonce,
            'X-Sign: ' . $sign
        ];

        $response = self::sendRequest(
            self::API_PAY_URL . '/axf/api/safeTradeInfo/downloadBill/' . $tradeId,
            $requestBody,
            $headers
        );
        echo $response . "\n";
    }



    public static function addRecharge()
    {
        $requestBody = '{"fee":0.1,"accountId":"XXX"}';
        $timestamp = (string) round(microtime(true) * 1000);
        $nonce = self::generateRandomString(16);

        $sign = self::calculateSign(self::APP_ID, $timestamp, $nonce, $requestBody, self::APP_SECRET);

        $headers = [
            'Content-Type: application/json',
            'X-App-Id: ' . self::APP_ID,
            'X-Timestamp: ' . $timestamp,
            'X-Nonce: ' . $nonce,
            'X-Sign: ' . $sign
        ];

        $response = self::sendRequest(
            self::API_PAY_URL . '/axf/api/recharge/add',
            $requestBody,
            $headers
        );
        echo $response . "\n";
    }

    public static function queryCurrency()
    {
        $requestBody = "";
        $timestamp = (string) round(microtime(true) * 1000);
        $nonce = self::generateRandomString(16);

        $sign = self::calculateSign(self::APP_ID, $timestamp, $nonce, $requestBody, self::APP_SECRET);

        $headers = [
            'Content-Type: application/json',
            'X-App-Id: ' . self::APP_ID,
            'X-Timestamp: ' . $timestamp,
            'X-Nonce: ' . $nonce,
            'X-Sign: ' . $sign
        ];

        $response = self::sendRequest(
            self::API_PAY_URL . '/axf/api/currency/query',
            $requestBody,
            $headers
        );
        echo $response . "\n";
    }

    public static function bookPay()
    {
        $requestBody = '{"accountId":"XXX","amount":0.1,"remark":"测试账本支付"}';
        $timestamp = (string) round(microtime(true) * 1000);
        $nonce = self::generateRandomString(16);

        $sign = self::calculateSign(self::APP_ID, $timestamp, $nonce, $requestBody, self::APP_SECRET);

        $headers = [
            'Content-Type: application/json',
            'X-App-Id: ' . self::APP_ID,
            'X-Timestamp: ' . $timestamp,
            'X-Nonce: ' . $nonce,
            'X-Sign: ' . $sign
        ];

        $response = self::sendRequest(
            self::API_PAY_URL . '/axf/api/safePayAccount/bookPay',
            $requestBody,
            $headers
        );
        echo $response . "\n";
    }

    public static function bookQuery()
    {
        $accountId = "XXX";
        $requestBody = "";
        $timestamp = (string) round(microtime(true) * 1000);
        $nonce = self::generateRandomString(16);

        $sign = self::calculateSign(self::APP_ID, $timestamp, $nonce, $requestBody, self::APP_SECRET);

        $headers = [
            'Content-Type: application/json',
            'X-App-Id: ' . self::APP_ID,
            'X-Timestamp: ' . $timestamp,
            'X-Nonce: ' . $nonce,
            'X-Sign: ' . $sign
        ];

        $response = self::sendRequest(
            self::API_PAY_URL . '/axf/api/safePayAccount/bookQuery/' . $accountId,
            $requestBody,
            $headers
        );
        echo $response . "\n";
    }

    public static function checkAccount()
    {
        $requestBody = '{"name":"XXX","identity":"<EMAIL>","companyName":"测试公司"}';
        $timestamp = (string) round(microtime(true) * 1000);
        $nonce = self::generateRandomString(16);

        $sign = self::calculateSign(self::APP_ID, $timestamp, $nonce, $requestBody, self::APP_SECRET);

        $headers = [
            'Content-Type: application/json',
            'X-App-Id: ' . self::APP_ID,
            'X-Timestamp: ' . $timestamp,
            'X-Nonce: ' . $nonce,
            'X-Sign: ' . $sign
        ];

        $response = self::sendRequest(
            self::API_PAY_URL . '/axf/api/checkAccount',
            $requestBody,
            $headers
        );
        echo $response . "\n";
    }

    public static function checkTrade()
    {
        $requestBody = '{"accountId":"XXX","orderType":"支转支","payerShowName":"XXX","records":[{"transferTitle":"","receiverName":"XXX","receiverPaymentAccount":"<EMAIL>","amount":1,"transferRemark":"","accountType":2,"instName":"","instBranchName":"","bankProvince":"","instCity":"","bankCode":""}]}';
        $timestamp = (string) round(microtime(true) * 1000);
        $nonce = self::generateRandomString(16);

        $sign = self::calculateSign(self::APP_ID, $timestamp, $nonce, $requestBody, self::APP_SECRET);

        $headers = [
            'Content-Type: application/json',
            'X-App-Id: ' . self::APP_ID,
            'X-Timestamp: ' . $timestamp,
            'X-Nonce: ' . $nonce,
            'X-Sign: ' . $sign
        ];

        $response = self::sendRequest(
            self::API_PAY_URL . '/axf/api/dun/checkTrade',
            $requestBody,
            $headers
        );
        echo $response . "\n";
    }
}

// 使用示例
// ApiPaySecurityDemo::addBankTradeInfo();
// ApiPaySecurityDemo::getTradeInfo();
// ApiPaySecurityDemo::downloadTradeInfo();
// ApiPaySecurityDemo::addAliTradeInfo();
// ApiPaySecurityDemo::addRecharge();
// ApiPaySecurityDemo::queryCurrency();
// ApiPaySecurityDemo::bookPay();
// ApiPaySecurityDemo::bookQuery();
// ApiPaySecurityDemo::checkAccount();
// ApiPaySecurityDemo::checkTrade();
