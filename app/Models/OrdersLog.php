<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class OrdersLog extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'orders_log';

    protected $fillable = [
        'user_id',
        'pay_channel_id',
        'date',
        'amount',
        'count',
        'Issued_count',
        'Issued_amount',
    ];

}
