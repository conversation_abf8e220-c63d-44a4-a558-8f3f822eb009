<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>超级收银台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f7f7f7;
            color: #333;
            line-height: 1.5;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            min-height: 100vh;
        }

        .header {
            background-color: #0096FF;
            color: white;
            padding: 16px;
            text-align: center;
            font-size: 18px;
            position: relative;
        }

        .amount-section {
            background: #fff;
            padding: 24px 16px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }

        .amount-label {
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .amount-display {
            font-size: 40px;
            font-weight: 500;
            color: #000;
        }

        .amount-display small {
            font-size: 24px;
        }

        .payment-form {
            padding: 20px 16px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .form-group select,
        .form-group input[type="text"] {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e5e5e5;
            border-radius: 4px;
            font-size: 16px;
            transition: all 0.3s;
        }

        .form-group select:focus,
        .form-group input[type="text"]:focus {
            border-color: #0096FF;
            outline: none;
            box-shadow: 0 0 0 2px rgba(7, 193, 96, 0.2);
        }

        .submit-button {
            width: 100%;
            padding: 12px;
            background-color: #0096FF;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .submit-button:hover {
            background-color: #0096FF;
        }

        .submit-button:active {
            background-color: #0096FF;
        }

        .payment-info {
            padding: 16px;
            background-color: #f8f8f8;
            margin: 16px;
            border-radius: 4px;
        }

        .payment-info p {
            color: #666;
            font-size: 14px;
            margin: 4px 0;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 16px;
            z-index: 1000;
        }

        .loading-spinner {
            width: 36px;
            height: 36px;
            margin-bottom: 16px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        @media (max-width: 480px) {
            .container {
                margin: 0;
            }

            .amount-display {
                font-size: 32px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            超级收银台
        </div>

        <div class="amount-section">
            <div class="amount-label">支付金额</div>
            <div class="amount-display">
                <small>¥</small><span id="displayAmount">0.00</span>
            </div>
        </div>

        <form id="paymentForm" class="payment-form">
            <!-- <div class="payment-info">
                <p>订单号：<span id="orderNumber">{{time()}}</span></p>
                <p>创建时间：<span id="orderTime">{{date('Y-m-d H:i:s')}}</span></p>
            </div> -->

            <div class="form-group">
                <label for="bank_code">支付方式：支付宝</label>
                <!-- <select id="bank_code" name="bank_code" disabled>
                    @foreach($bank_arr as $k => $v)
                        <option value="{{$k}}">{{$v}}</option>
                    @endforeach
                </select> -->
            </div>

            <div class="form-group">
                <label for="amount">支付金额</label>
                <input type="text" id="amount" name="amount" placeholder="请输入支付金额" required step="0.01"
                    inputmode="decimal">
                <input type="hidden" id="agent_id" name="agent_id" value="{{ $_GET['agent_id'] ?? '' }}">
            </div>

            <button type="submit" class="submit-button">立即支付</button>
        </form>
    </div>

    <div id="overlay" class="overlay" style="display: none;">
        <div class="loading-spinner"></div>
        <span>订单创建中...</span>
        <span style="margin-top: 8px;">剩余 <span id="countdown">30</span> 秒</span>
    </div>

    <script>
        // 金额输入实时显示
        document.getElementById('amount').addEventListener('input', function (e) {
            let value = this.value.replace(/[^\d.]/g, '');
            value = value.replace(/^\./g, '0.');
            value = value.replace(/\.{2,}/g, '.');
            value = value.replace(/^0+(?=\d)/g, '');
            value = value.replace(/^(\d{5,})/g, '$1');
            value = value.replace(/(\.\d{2}).*$/, '$1');
            if (value && !value.includes('.')) {
                value = value.substring(0, 5);
            }
            this.value = value;
            document.getElementById('displayAmount').textContent = value || '0.00';
        });

        document.getElementById('paymentForm').addEventListener('submit', function (event) {
            event.preventDefault();

            var overlay = document.getElementById('overlay');
            overlay.style.display = 'flex';

            var formData = new FormData(this);

            var countdownElement = document.getElementById('countdown');
            var countdown = 30;
            countdownElement.textContent = countdown;

            var interval = setInterval(function () {
                countdown--;
                countdownElement.textContent = countdown;
                if (countdown <= 0) {
                    clearInterval(interval);
                    overlay.style.display = 'none';
                }
            }, 1000);

            fetch('/api/view/addOrder', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    clearInterval(interval);
                    overlay.style.display = 'none';

                    if (data.code === 200) {
                        window.location.href = data.result.url;
                    } else {
                        alert(data.message || '支付请求失败');
                    }
                })
                .catch(error => {
                    clearInterval(interval);
                    overlay.style.display = 'none';
                    alert('请求发生错误');
                    console.error('Error:', error);
                });
        });
    </script>
</body>

</html>