<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('pay_channel_id')->nullable()->comment('支付通道');
            $table->string('batch_no')->nullable()->comment('商户订单号');
            $table->string('order_id')->nullable()->comment('平台订单号');
            $table->string('pay_order')->nullable()->comment('支付订单号');
            $table->integer('user_id')->nullable()->comment('老板id');
            $table->decimal('amount')->nullable()->comment('金额');
            $table->integer('is_show')->default('1')->nullable()->comment('是否已经打开 1未打开 2已打开');
            $table->integer('status')->default('1')->nullable()->comment('状态 1未支付 2支付成功 3超时 4支付失败 5订单金额与实际不否 6未创建订单且成功回调');
            $table->string('goods_name')->nullable()->comment('订单标题');
            $table->decimal('amount_san')->comment('实际支付');
            $table->timestamp('add_at')->nullable()->comment('下单时间');
            $table->timestamp('pay_at')->nullable()->comment('支付时间');
            $table->text('qr_code')->nullable()->comment('支付地址');
            $table->string('tradeType')->default('1')->nullable()->comment('支付类型');
            $table->string('error_msg')->nullable();
            $table->string('notify_url')->nullable()->comment('回调地址');
            $table->string('return_url')->nullable()->comment('同步回调地址');
            $table->string('user_status')->nullable()->comment('用户响应  success 响应成功，其他的每隔 5分钟回调一次，共3次');
            $table->timestamp('user_notify_at')->nullable()->comment('用户回调时间');
            $table->integer('user_notify_num')->default('0')->nullable()->comment('用户回调次数');
            $table->string('desc')->nullable()->comment('备注');
            $table->string('user_ip')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order');
    }
}
