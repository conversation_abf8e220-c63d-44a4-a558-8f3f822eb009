<?php
/*
 * @Author: your name
 * @Date: 2021-03-09 20:48:06
 *
 * @LastEditTime: 2021-07-01 11:00:53
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \huoke\app\Http\Controllers\Api\BaseController.php
 */

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Order;
use GuzzleHttp\Client;
use BsPaySdk\core\BsPay;
use App\Jobs\RechargeJob;
use Illuminate\Http\Request;
use GuzzleHttp\Promise\Utils;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;
use App\Http\Controllers\BaseController;
use App\Http\Controllers\kq\PayBankController;

class TestController extends BaseController
{
    private $config;

    public function __construct($id = 0)
    {
        // $config = [
        //     "sys_id" => "****************",
        //     "huifu_id" => "****************",
        //     "product_id" => "KAZX",
        //     "rsa_merch_private_key" => "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCG/i/OkPLfWcQKrRCW9Zpu2LPYj2VJPIpuQknS170UltBEb63Tk/yiPJ4qN47Qi5k0JPRoftMD7IOECmxtZhBgIj/FC+AIWJFsvqe1w32gyogHP/JAEw8LgPkCwMwCEeItnS1sj6smuMnB1Fvoc/4JHD7dUYyhBFCFpmOZhHzUP4y2fC53E+4Eb7kStSz+iCyex1rmwRy/psDU9oQyVekeJbW2G/pZdt3aB8/9l74e86MMtOmDXWz9hU7y+2y3od2sUzowNTMfmDFpFTqkqGDa5Hr7evAJdEK7fD6/cnYeHWJPBPaWNrM9w1owGnzZ5jKnlbHuB/sf4WH7PmLTgOBVAgMBAAECggEACQto2pc9npdeGjUEQokMItckHaNl7uU+hSqt1ZGc2o9MPvmYlO96Ysha9IsqZ3CjACplscaljsK/AQ7Nuvq2qCpDSYmFJioA/F89z2DcvvQy7wFq0r40KNooVPXj9VOLavD4BSkPF5YT9oeq7MUwxR1+XMfE+1jVqE7Ap52H25SZL7zyygaPsOfqT9dnRpxjF1cxl19YM3ILOJ1sii0G/yU2Fy9EephSVSok4fKciJ+ibR1ZqGQCPeefCMG8LjXTCOaxGSFVpdewfx/SRpZW7hnUmYPcAPL88bFq9hfo0Cj41XTdugiXQBgI4OuuiDRQGqReupivM9LZYWZC6QXNQQKBgQDbqnIylQKiWhD6ULPbelrqv/BmsTrX1jC4fvAWJirvwJ0PvEQO/tfC824C8FjZXaxDZ7XcQ58Z+pZTPGT/UZYn1kvg4crdury6dH+3azZfpHnbw4/tAegiKnbBTXNOMCXREORsyS9DB2GVPTqPvDWkSI45XaEziBOjqnUD2Yrl5QKBgQCdUlecNmilp7YRRJ27vXK2FJOew9dVIcQAIq68bXB1enBhn79V/qJQ2/ZScHH+3DQ/ehmIoRE/87mVQZc/SSDNDk4gW0ZRAtVhr7ZBJTy+5giEybPFPdN+cyZzRWdnW2kjYORcY+TKexotXllmNsSm6yhF2BZz19C0cr0cFqlpsQKBgQCUoafTge/eWoLSXHjWMNwDqwvG8HSYRl2A+KMK2ZiD0Rh05TZHzze+uWfepTH5IeDFWw86kqa8tUmx2FOeTl4DZoTBDQZvMUMyBlHEkc6guHGbuZ9RBtFDDOh9syIp7XopE/z7fsVz3TKyAFP3nanOFynktu20KIbhXxz+Fdxd4QKBgAInO12+/OWvivVnjAUizawiUfbtVUgsAyPBVcTsdoqVF26uog7KQmx65j8wnM7RMsHfqPPOyI6ohlS9phVOqe1EkdsthKPdNxB0ODliChfOzDUNEbHUa82iQ9d2DrlCbgj27Yk6MMp4rt4KGY0lmMF0HIQANiZ1wLyHiYEV6lGxAoGBAKTbRnXXAykLu0j2CsbbbKEl7xDPE6zM4K3USKj670KPBMZmDQ6CFRZoPYneMDBzGQb4NQL77tS63tkeTE531cjJq1SAzviB7RMmqNQ21+gtDs+nB7X3UAM0ZWDhdPS+JWO3FNPc2jxicKng411Wb9nRmz1Y0l0kxH+NA9jjyaMB",
        //     "rsa_huifu_public_key" => "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAi9NtaniV66Wuk9ES16NLmKaQpBTBbf76amSYrY8FRKXDB9/ZzVA5jQEh7BPpYnYx3aERYQqkpPgm34QQGPQ89/2MMO7ejSpvF08X8nFLvqbbKQ/+YA7sqbagypLN9fTx77CW/nIXXLRvpKqGNOn4Wt7x07Ke/S50rGPk46MAonXQ0skO01eeQiTjWZP6R14m4XR2hwSjxNuZr8Vt6oNwV5+RRmDD91JktFx0AO+KyMNxuBkFRXQ3gpFXQgZKPS5CgNcchhHbFlQKbc/PqGn7L9TPNaEoXhg635Vn1foxHWBPmgaoWhlJIkMSGdmcds9rRG3HkhZpNx7g9XZsNiT/8QIDAQAB",
        //     "wx_appid" => "wxc8f8c79ef11040dc",
        //     "wx_secret" => "065d6c62dc43b4427167c155d991135d"
        // ];
        // $this->config = $config;
        // BsPay::init($config, true);
    }

    public function aaa()
    {
        $arr = [
            '{"head":{"version":"1.0.0","messageType":"C1011","memberCode":"10222303931","externalRefNumber":"2412131236072908004225","origMessageType":"C1019"},"requestBody":{"signedData":"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","envelopedData":"MIAGCSqGSIb3DQEHA6CAMIACAQAxggGAMIIBfAIBADBkMFgxCzAJBgNVBAYTAkNOMTAwLgYDVQQKDCdDaGluYSBGaW5hbmNpYWwgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkxFzAVBgNVBAMMDkNGQ0EgQUNTIE9DQTMzAggzAAAElSeUNjANBgkqhkiG9w0BAQEFAASCAQBZjBeMohdNOmvq+by5EYwyChEs9RzaBxo3TE5UO8oYI3BLED7yGljc7Fe+gtUmOd+3hJk3QuRcbfFC8AvOaFbfflyhqSMb9lWmL0wK9lTcfp7duRI7Kbn4axT6x0GcWIZ+f7CtE5w8lpXm7q4AeW9QWStpvQK8m3T/9hlol3kVXx5HvQUpwkpKIREQj//XMSgonmeoXMWvBTfZYbi7fK9rkmP7O6TYNN+VVLxPcZFl9nbmIa3p/XER/fnlMxuKrnpqHkMXIORUp2jIYMxL/JZDvSNHztHpo3ZZ/3r5D6+qcDehVwwwSOSGd1wYjAuoLOCFihdaMXCd0a0S7ovhrFW1MIAGCSqGSIb3DQEHATAdBglghkgBZQMEAQIEEAAAAAAAAAAAAAAAAAAAAACggASCARBmRo6yiSFVk75XNFz/B9DE+CvgoOrWXONF0+Ajj3Vne1pWimtOkZ1q5tadZ7+aMIdGli2S+1kV+mSwOB0IpJOkiafIhiMBckJL2Wo/0Bi5h1KjCeH8LkuHQBITAacgXLC8kRc1JNJu76VNPMhsdaK4GtZGGzRz66pHbTQUNE0S12KKwiXqjH6fsWOB6UoQo8J1+rdPn/Pjy94rjvsKhg/cYSR/1eR9kWqtcNdfXiVVl9wcZWIZG6jh/s4vk8d+W0lAPmT/xNglDR+TNCDhf/+k2c/GnvqsRqkZLZK6erp7STVoceHPBB5Er346ieTmIj+ndUZA1p395GaClPH0ubU2iqt4IUUWZxz6K73oE6wC/gAAAAAAAAAAAAA="}}',
            '{"head":{"version":"1.0.0","messageType":"C1011","memberCode":"10222303931","externalRefNumber":"2412131254130021798331","origMessageType":"C1019"},"requestBody":{"signedData":"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","envelopedData":"MIAGCSqGSIb3DQEHA6CAMIACAQAxggGAMIIBfAIBADBkMFgxCzAJBgNVBAYTAkNOMTAwLgYDVQQKDCdDaGluYSBGaW5hbmNpYWwgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkxFzAVBgNVBAMMDkNGQ0EgQUNTIE9DQTMzAggzAAAElSeUNjANBgkqhkiG9w0BAQEFAASCAQAlj7CShiyIu/2OyApjukodVLcaoX6r6ZMVlf37gs3pwmpbsygJB7qsYmJgcGAWGHlLvpCsTPTT1NqYwloVlC5C3Rz628IsMgK7ZQz2zfEVDOFYzNpfAx1YNX6oi11EdSiydedSJDd5mCAPcFqVCG7uIDj/BOnAY1rmWK9WyCrt4GBQfa/JYJYGTdaYZH5j32aV7822QCuoW8+poMaPobf06xg6qSLyQAwyNkJWGIPIIaP4PQ4zabbl8zWq6pnlkOTnc1Z5wQYrB8heYsNj27nx2zd0+6jy12gjVsoQC1QqcVcnrnUhjFNm8FY2G2AnFbKzvNe4GazWz6BC+XRScYfWMIAGCSqGSIb3DQEHATAdBglghkgBZQMEAQIEEAAAAAAAAAAAAAAAAAAAAACggASCARDEPqNnecLIVsgHJFrTxmu7nb/6G/eWI6wAH/1jd2l1pYhiPUyKUzvE6TWBtwZvWjeL84YWJk8kakDLJqyO+u5IdVvTww8AqOzzCN41Gbq+oa5RdQoUqJqeYB6mzw/xxNfU69Dw3wSL3FeZw/pI7k3gqa06h8SGPw7x9Ykx5DI1pSDq4SMISMbjA+lDaPSdCeyOM7FaXkl0cZFlElBMAsFu47WzBWFR1wW66LDiSVtk9RL20TC8a/IeRQVFz4rFmegNvaAXRaVfe0J+YMNmk9cgN0gG+vOSWAI4dFx+P9l/K/mo310i168u5sVOmb54kep6pza0ffMQTpaUzRHaQP8IydbV1W/xDmE7nlFmer8bcwAAAAAAAAAAAAA="}}',
        ];
        $client = new Client();
        foreach ($arr as $key => $value) {
            $aa = json_decode($value, 1);
            $promises[$key] = $client->postAsync('http://xinxinpay.me/api/recharge/notice/kq', [
                'json' => $aa
            ]);
        }

        // 并发执行所有请求
        $results = Utils::settle($promises)->wait();

        foreach ($results as $key => $result) {
            if ($result['state'] === 'fulfilled') {
                $response = $result['value'];
                $remainingBytes = $response->getBody()->getContents();
                print_r($remainingBytes);
            } else {
                // 处理错误情况
                echo "Request {$key} failed: " . $result['reason']->getMessage();
            }
        }

        // $data = (new PayBankController())->C1019([
        //     'externalRefNumber' => '2412061223401824419337',
        //     'amount' => '200',
        //     'cardHolderName' => '管跃',
        //     'bankName' => '大连银行',
        //     'pan' => '6213361739910693877',
        //     'pay_channel_id' => 8,
        // ]);
        // dd($data);
        // dispatch(new RechargeJob(43, 1859869))->onQueue('Recharge');
        // dispatch(new RechargeJob(11, 1859869))->onQueue('Recharge');
    }
    /**
     * 支付跳转
     *
     * @param Request $request
     */
    public function view(Request $request)
    {
        return view('api.Cashier', [
            // 'bank_arr' => ["A_NATIVE" => "支付宝", "KQ_QUICK" => "快钱支付"],
            'bank_arr' => ["A_NATIVE" => "支付宝"],
        ]);
    }

    /**
     * 收银台页面，点击直接跳转支付页面
     *
     * @param Request $request
     */
    public function cash(Request $request)
    {
        $pay_channel_id = 1;
        $agent_id = 1;
        $visitKey = 'user_channel_time:' . 'id:' . $pay_channel_id . 'agent_id:' . $agent_id;

        Redis::expire($visitKey, 30);
        dd();
        $order_id = $request->get('order_no', '');
        $Order = Order::where(['order_id' => $order_id, 'status' => 1, 'is_show' => 1])->first();

        if ($Order) {
            return view('api.Cashier2', [
                // 'bank_arr' => ["A_NATIVE" => "支付宝", "KQ_QUICK" => "快钱支付"],
                'bank_arr' => ["A_NATIVE" => "支付宝"],
                'userOrderId' => $Order->order_id,
                'amount' => $Order->amount,
                'created_at' => $Order->created_at,
            ]);
        }
        return view('api.Cashier2', [
            // 'bank_arr' => ["A_NATIVE" => "支付宝", "KQ_QUICK" => "快钱支付"],
            'bank_arr' => ["A_NATIVE" => "支付宝"],
            'userOrderId' => '订单已存在，或重新发起',
            'amount' => 0,
            'created_at' => ''
        ]);
    }
    /**
     * 点击直接跳转支付页面
     * @param \Illuminate\Http\Request $request
     */
    public function cashAddOrder(Request $request)
    {
        //
        $order_id = $request->post('order_id', '');
        $Order = Order::where(['order_id' => $order_id, 'status' => 1, 'is_show' => 1])->first();

        if ($Order) {
            $ip = $request->ip();
            $OrderPayIpCount = Order::where('created_at', 'like', date('Y-m-d') . '%')
                ->where('user_ip', $ip)
                ->count('id');
            $ip_count = User::where('agent_id', $Order->user_id)->value('ip_count');
            if ($ip_count > 0 && $OrderPayIpCount > $ip_count) {
                return $this->responseJson(400, "请求频繁，请稍后再试");
            }
            // 微信打开过
            // Order::where(['order_id' => $order_id])->update(['user_ip' => $request->ip()]);
            Order::where(['order_id' => $order_id])->update(['is_show' => 2, 'user_ip' => $ip]);
            return $this->responseJson(200, "请求成功", [
                'url' => $Order->qr_code,
            ]);
        }
        return $this->responseJson(400, "订单不存在");
    }

    public function viewAddOrder(Request $request)
    {
        try {
            // 合并新数据到请求对象
            request()->merge([
                'agent_id' => $request->input('agent_id', '********'),
                'batch_no' => generateOrderNumber(),
                'amount' => $request->input('amount'),
                'tradeType' => $request->input('bank_code', 'A_NATIVE'),
                'notify_url' => 'http://api.xhl001.xyz/api/user',
            ]);

            $tradePay = (new OrderController())->addOrder(request());
            $bodydata = $tradePay->getData(true);
            return $bodydata;
        } catch (\Exception $e) {
            Log::channel('user_add_order')->info('执行失败', [$e->getMessage()]);
            return $this->responseJson(400, $e->getMessage());
        }

        if ($bodydata['code'] == 200) {
            $pay_url = $bodydata['result']['url'];
            Header("Location:$pay_url");
            die;
        }

        echo $bodydata['message'];
    }
}
