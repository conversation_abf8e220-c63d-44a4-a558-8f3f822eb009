# Signature Utility Class

This document describes the `App\Utils\Signature` class, which is a PHP port of the Java signature utility class for GatePay.

## Overview

The Signature class provides cryptographic signature generation and verification functionality using HMAC-SHA512 algorithm. It supports both hexadecimal and Base64 encoded signatures with constant-time comparison to prevent timing attacks.

## Features

- **HMAC-SHA512 Signature Generation**: Generate secure signatures using HMAC-SHA512 algorithm
- **Multiple Encoding Formats**: Support for both hexadecimal and Base64 encoded signatures
- **Signature Verification**: Verify signatures with constant-time comparison to prevent timing attacks
- **Utility Functions**: Generate random nonce strings and current timestamps
- **Error Handling**: Comprehensive error handling with meaningful exceptions

## Installation

The class is located at `app/Utils/Signature.php` and uses the `App\Utils` namespace.

## Usage

### Basic Signature Generation (Hex Format)

```php
use App\Utils\Signature;

$timestamp = '1631257823000';
$nonce = 'abcd1234';
$body = 'the post request body content';
$secretKey = 'your-secret-key';

// Generate hex signature
$signature = Signature::verifySignature($timestamp, $nonce, $body, $secretKey);
echo "Signature: " . $signature;
```

### Base64 Signature Generation

```php
// Generate Base64 signature
$base64Signature = Signature::generateSignature($timestamp, $nonce, $body, $secretKey);
echo "Base64 Signature: " . $base64Signature;
```

### Signature Verification

```php
// Verify Base64 signature
$isValid = Signature::verifyBase64Signature($timestamp, $nonce, $body, $signature, $secretKey);
if ($isValid) {
    echo "Signature is valid";
} else {
    echo "Signature is invalid";
}

// Verify hex signature
$payload = sprintf("%s\n%s\n%s\n", $timestamp, $nonce, $body);
$result = Signature::verify($payload, $hexSignature, $secretKey);
if ($result === null) {
    echo "Signature is valid";
} else {
    echo "Signature is invalid: " . $result;
}
```

### Utility Functions

```php
// Generate random nonce
$nonce = Signature::generateNonce(); // 16 characters by default
$customNonce = Signature::generateNonce(32); // Custom length

// Get current timestamp in milliseconds
$timestamp = Signature::getCurrentTimestamp();
```

## API Reference

### Methods

#### `verifySignature(string $timestamp, string $nonce, string $body, string $secretKey): string`
Generates a hex-encoded signature using the format: `timestamp\nnonce\nbody\n`

#### `generateSignature(string $timestamp, string $nonce, string $body, string $secretKey): string`
Generates a Base64-encoded signature using the format: `timestampnoncebody`

#### `verifyBase64Signature(string $timestamp, string $nonce, string $body, string $signature, string $secretKey): bool`
Verifies a Base64-encoded signature and returns true if valid, false otherwise.

#### `sign(string $signingData, string $key): string`
Signs arbitrary data and returns a hex-encoded signature.

#### `verify(string $signingData, string $signature, string $key): string|null`
Verifies a hex-encoded signature. Returns null if valid, error message if invalid.

#### `generateNonce(int $length = 16): string`
Generates a random alphanumeric nonce string of specified length.

#### `getCurrentTimestamp(): string`
Returns the current timestamp in milliseconds as a string.

### Constants

- `INVALID_SIGNATURE`: Error message returned when signature verification fails

## Security Features

1. **Constant-Time Comparison**: Uses constant-time comparison to prevent timing attacks
2. **HMAC-SHA512**: Uses strong cryptographic hash function
3. **Proper Error Handling**: Catches and handles cryptographic exceptions appropriately

## Examples

See `examples/SignatureUsageExample.php` for comprehensive usage examples.

## Testing

Run the unit tests with:

```bash
php artisan test tests/Unit/SignatureTest.php
```

## Migration from Java

This PHP class is a direct port of the Java Signature class with the following key differences:

1. **Method Names**: Maintained the same method names for consistency
2. **Return Types**: PHP uses nullable return types (`?string`) instead of Java's null returns
3. **Exception Handling**: Uses PHP's `\RuntimeException` instead of Java's `RuntimeException`
4. **String Handling**: PHP's string functions are used instead of Java's byte array operations

## Error Handling

The class throws `\RuntimeException` for cryptographic errors and `\InvalidArgumentException` for invalid input parameters.

## Performance Considerations

- Uses PHP's built-in `hash_hmac()` function for optimal performance
- Implements constant-time comparison to prevent timing attacks
- Minimal memory allocation for large signature operations

## Compatibility

- PHP 7.4+
- Requires `hash` extension (typically enabled by default)
- Compatible with Laravel framework
