<?php
namespace App\Http\Controllers\kq;
use Exception;
use App\Models\Kq;
use App\Models\Order;
use App\Models\UsersBank;
use App\Models\PayChannel;
use App\Models\UserBbBank;
use remote\RemoteProcessor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Validator;

/**
 * 认证支付再次接口
 */

class SecondCertificationController extends BaseController
{
    /**
     * 获取银行列表
     * @return \Illuminate\Http\JsonResponse
     */
    public function getKqBankMap()
    {
        // 
        $arr = [];
        $data = Kq::getKqBankMap();
        foreach ($data as $key => $value) {
            $arr[] = [
                'value' => $key,
                'text' => $value,
                'agreement' => Kq::getKqBankAgreementMap($key),
            ];
        }
        return $this->responseJson(200, '成功', $data);
    }

    /**
     * 获取用户绑定的银行卡信息
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserBankInfo(Request $request)
    {
        $validator = Validator::make($request->post(), [
            'order_id' => 'required|regex:/^[^<>&;]*$/',
            'name' => 'required|regex:/^[\p{Han}a-zA-Z\s]+$/u', // 只允许汉字、字母和空格
            'idcard' => ['required', 'regex:/^(?:\d{15}|\d{17}[\dXx])$/'],
        ], [
            'order_id.required' => '订单号不能为空',
            'name.required' => '持卡人不能为空',
            'name.regex' => '持卡人姓名只能包含汉字、字母和空格',
            'idcard.required' => '持卡人身份证不能为空',
            'idcard.regex' => '身份证号必须为15或18位数字或X/x',
            'order_id.regex' => '订单号内容中不允许包含特殊字符',
        ]);

        if ($validator->fails() == true) {
            $errorMessage = $validator->errors()->first();
            return $this->responseJson(400, $errorMessage);
        }

        $order_id = $request->post('order_id', '');
        $name = $request->post('name', '');
        $idcard = $request->post('idcard', '');

        $Order = Order::where(['order_id' => $order_id, 'status' => 1, 'is_show' => 1])->first();
        if (!$Order) {
            return $this->responseJson(400, '订单不存在或状态不正确');
        }
        $pay_channel_id = $Order->pay_channel_id;
        $arr = [];
        UserBbBank::where(['idcard' => $idcard, 'name' => $name, 'pay_channel_id' => $pay_channel_id, 'attestation' => 2])
            ->select(['bank_type', 'num', 'bank_id as customerId', 'token_no as payToken', 'bank_id'])->get()->each(function ($item) use (&$arr) {
                $item = $item->toArray();
                $num = substr($item['num'], -4);
                $bank_id = $item['bank_id'];
                $phoneNumber = UsersBank::where('id', $bank_id)->value('mobile_no');
                // 确保手机号长度为11位
                if (strlen($phoneNumber) !== 11) {
                    return $phoneNumber; // 如果不是11位，直接返回原号码
                }
                // 使用 substr_replace 替换中间四位
                $maskedNumber = substr_replace($phoneNumber, '****', 3, 4);

                $arr[] = [
                    'mobile_no' => $maskedNumber,
                    'customerId' => $item['customerId'],
                    'payToken' => $item['payToken'],
                    'bank_type' => Kq::getKqBankIdMap($item['bank_type']) . "储蓄卡（尾号{$num}）",
                    'agreement' => Kq::getKqBankAgreementMap($item['bank_type']),
                ];
            });
        return $this->responseJson(200, '成功', $arr);
    }

    /**
     * 认证支付申请
     * @param $rawData 前端传过来的json字符串
     * 
     * 返回数据
     * * [
     *     "head" => [
     *         "version" => "1.0.0",
     *         "messageType" => "A2003",
     *         "memberCode" => "***********",
     *         "responseCode" => "0000",
     *         "responseTextMessage" => "成功",
     *         "externalRefNumber" => "1733121669_cert_apply",
     *     ],
     *     "responseBody" => [
     *         "bizResponseCode" => "0000",
     *         "bizResponseMessage" => "交易成功",
     *         "merchantId" => "***************",
     *         "customerId" => "1",
     *         "payToken" => "8120000000002958088",
     *         "token" => "**********",
     *     ]
     * ];
     */
    public function A2003(Request $request)
    {
        try {
            //code...
            global $config;
            $log = 'kq_log';

            Log::channel($log)->info('------------------------------------------------------------');
            Log::channel($log)->info('A2003接口接收到前端数据：', [$request->all()]);

            $validator = Validator::make($request->post(), [
                'order_id' => 'required|regex:/^[^<>&;]*$/',
                'customerId' => 'required|digits_between:1,12',
                'payToken' => 'required|digits:19',
            ], [
                'order_id.required' => '订单号不能为空',
                'order_id.regex' => '订单号内容中不允许包含特殊字符',
                'customerId.required' => '客户号不能为空',
                'customerId.digits_between' => '客户号必须为1到12位数字',
                'payToken.required' => '支付token不能为空',
                'payToken.digits' => '支付token必须为19位数字',
            ]);

            if ($validator->fails() == true) {
                $errorMessage = $validator->errors()->first();
                Log::channel($log)->info($errorMessage);
                Log::channel($log)->info('失败');
                Log::channel($log)->info('------------------------------------------------------------');
                return $this->responseJson(400, $errorMessage);
            }

            $order_id = $request->post('order_id', '');
            $payToken = $request->post('payToken', '');
            $customerId = $request->post('customerId', '');
            $Order = Order::where(['order_id' => $order_id, 'status' => 1, 'is_show' => 1])->first();
            if (!$Order) {
                throw new Exception('订单不存在或状态不正确');
            }

            $dataJson = PayChannel::dataJson($Order->pay_channel_id);
            $config = array_merge($config, $dataJson);
            //开始组装报文
            $arrayData = [
                'customerId' => $customerId,
                'payToken' => $payToken,
                'amount' => $Order->amount * 100,
            ];
            //---1.组装明文head数组
            $request_Head_Array = array();
            $request_Head_Array['version'] = "1.0.0";
            $request_Head_Array['messageType'] = "A2003";
            $request_Head_Array['memberCode'] = $config['account']['memberCode'];
            // $request_Head_Array['externalRefNumber'] = time() . "_cert_apply";
            $request_Head_Array['externalRefNumber'] = $order_id;
            //---2.组装明文body数组
            $request_Body_Array['merchantId'] = $config['account']['merchantId'];
            $request_Body_Array['customerId'] = $arrayData['customerId'];
            $request_Body_Array['payToken'] = $arrayData['payToken'];
            $request_Body_Array['amount'] = $arrayData['amount'];

            /**
             * 请求快钱，获取返回的明文
             * 提醒注意：如涉及敏感信息，请加工处理后再给前端
             *
             */
            $responseDecryptMessage = remoteProcessor::requestKQ($request_Head_Array, $request_Body_Array, $config);
            Log::channel($log)->info('A2003接口收到快钱返回明文数据：' . $responseDecryptMessage);
            Log::channel($log)->info('------------------------------------------------------------');
            $data = json_decode($responseDecryptMessage, true);
            if (!$data) {
                throw new Exception('配置错误');
            }

            $bizResponseCode = $data['responseBody']['bizResponseCode'];
            $bizResponseMessage = $data['responseBody']['bizResponseMessage'];
            if ('0000' != $bizResponseCode) {
                throw new Exception($bizResponseMessage);
            }
            $token = $data['responseBody']['token'];
            $payToken = $data['responseBody']['payToken'];

            return $this->responseJson(200, '成功', [
                'token' => $token,
                'payToken' => $payToken,
                // 'externalRefNumber' => $request_Head_Array['externalRefNumber'],
            ]);

        } catch (\Throwable $th) {
            //throw $th;
            Log::channel($log)->info('A2003接口失败：' . $th->getMessage(), [$th->getLine(), $th->getFile()]);
            Log::channel($log)->info('------------------------------------------------------------');
            return $this->responseJson(400, $th->getMessage());
        }
    }

    /**
     * 认证支付
     * @param $rawData 前端传过来的json字符串
     */
    public function A2004(Request $request)
    {
        DB::beginTransaction();
        $log = 'kq_log';
        try {
            //code...
            global $config;

            Log::channel($log)->info('------------------------------------------------------------');
            Log::channel($log)->info('A2004接口接收到前端数据：', [$request->all()]);

            $validator = Validator::make($request->post(), [
                'order_id' => 'required|regex:/^[^<>&;]*$/',
                'token' => 'required|digits_between:9,12',
                'validCode' => 'required|digits:6',
                'payToken' => 'required|digits:19',
            ], [
                'order_id.required' => '订单号不能为空',
                'order_id.regex' => '订单号内容中不允许包含特殊字符',
                'token.required' => 'token不能为空',
                'token.digits_between' => 'token必须为9到12位数字',
                'validCode.required' => '验证码不能为空',
                'validCode.digits' => '验证码必须为6位数字',
                'payToken.required' => '支付token不能为空',
                'payToken.digits' => '支付token必须为19位数字',
            ]);

            if ($validator->fails() == true) {
                $errorMessage = $validator->errors()->first();
                Log::channel($log)->info($errorMessage);
                Log::channel($log)->info('失败');
                Log::channel($log)->info('------------------------------------------------------------');
                return $this->responseJson(400, $errorMessage);
            }

            $token = $request->post('token');
            $validCode = $request->post('validCode');
            $payToken = $request->post('payToken');
            $order_id = $request->post('order_id');

            $Order = Order::where(['order_id' => $order_id, 'status' => 1, 'is_show' => 1])->first();
            if (!$Order) {
                throw new Exception('订单不存在或状态不正确');
            }

            $UserBbBank = UserBbBank::where('token_no', $payToken)->first();
            if (!$UserBbBank) {
                throw new Exception('payToken不正确');
            }

            $dataJson = PayChannel::dataJson($Order->pay_channel_id);
            $config = array_merge($config, $dataJson);

            //开始组装报文
            $arrayData = [
                'externalRefNumber' => $order_id,
                'phoneNo' => UsersBank::where('id', $UserBbBank->bank_id)->value('mobile_no'),
                'amount' => $Order->amount * 100,
                'validCode' => $validCode,
                'token' => $token,
                'customerId' => $UserBbBank->bank_id,
                'payToken' => $payToken,
                'tr3Url' => rtrim(env('KQ_URL'), '/') . '/api/order/notice/kq',
            ];

            //---1.组装明文head数组
            $request_Head_Array = array();
            $request_Head_Array['version'] = "1.0.0";
            $request_Head_Array['messageType'] = "A2004";
            $request_Head_Array['memberCode'] = $config['account']['memberCode'];
            //注意，此处externalRefNumber须与A2003的externalRefNumber相同
            $request_Head_Array['externalRefNumber'] = $arrayData['externalRefNumber'];
            //---2.组装明文body数组
            $request_Body_Array['merchantId'] = $config['account']['merchantId'];
            $request_Body_Array['terminalId'] = $config['account']['terminalId'];
            $request_Body_Array['entryTime'] = date('YmdHis', time());
            $request_Body_Array['customerId'] = $arrayData['customerId'];
            $request_Body_Array['phoneNo'] = $arrayData['phoneNo'];
            $request_Body_Array['amount'] = $arrayData['amount'];
            $request_Body_Array['validCode'] = $arrayData['validCode'];
            $request_Body_Array['token'] = $arrayData['token'];
            $request_Body_Array['payToken'] = $arrayData['payToken'];
            $request_Body_Array['tr3Url'] = $arrayData['tr3Url'];

            /**
             * 请求快钱，获取返回的明文
             * 提醒注意：如涉及敏感信息，请加工处理后再给前端
             *
             */
            $responseDecryptMessage = remoteProcessor::requestKQ($request_Head_Array, $request_Body_Array, $config);
            Log::channel($log)->info('A2004接口收到快钱返回明文数据：' . $responseDecryptMessage);
            Log::channel($log)->info('------------------------------------------------------------');
            $data = json_decode($responseDecryptMessage, true);
            if (!$data) {
                throw new Exception('配置错误');
            }

            $bizResponseCode = $data['responseBody']['bizResponseCode'];
            $bizResponseMessage = $data['responseBody']['bizResponseMessage'];
            if ('0000' != $bizResponseCode) {
                throw new Exception($bizResponseMessage);
            }

            $Order->is_show = 2;
            $Order->error_msg = '支付成功，请等待通知';
            $Order->save();
            DB::commit();

            return $this->responseJson(200, '支付成功', []);
        } catch (\Throwable $th) {
            //throw $th;
            Log::channel($log)->info('A2004接口失败：' . $th->getMessage(), [$th->getLine(), $th->getFile()]);
            Log::channel($log)->info('------------------------------------------------------------');
            DB::rollBack();
            return $this->responseJson(400, $th->getMessage());
        }
    }

}