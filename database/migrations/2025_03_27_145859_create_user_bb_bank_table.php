<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUserBbBankTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_bb_bank', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('bank_id')->nullable()->comment('商户银行ID');
            $table->string('user_id')->default('')->comment('商户号');
            $table->integer('pay_channel_id')->comment('通道ID');
            $table->string('num')->nullable()->comment('银行卡号');
            $table->string('name')->nullable()->comment('姓名');
            $table->string('idcard')->nullable()->comment('身份证号');
            $table->integer('status')->default('1')->nullable()->comment('是否认证1未认证 2已认证 3认证失败');
            $table->string('huifu_id')->nullable();
            $table->string('msg')->nullable()->comment('信息');
            $table->integer('attestation')->default('1')->nullable()->comment('状态：1未绑定 2已绑定 3绑定失败');
            $table->string('token_no')->nullable()->comment('取现卡序列号');
            $table->decimal('amount')->default('0.00')->nullable()->comment('余额');
            $table->string('bank_type')->nullable()->comment('银行编码');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_bb_bank');
    }
}
