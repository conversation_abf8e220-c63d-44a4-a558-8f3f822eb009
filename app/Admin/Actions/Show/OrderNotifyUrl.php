<?php
/*
 * @Author: your name
 * @Date: 2021-04-01 17:06:30
 * @LastEditTime: 2021-05-21 14:43:25
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \huoke\app\Admin\Actions\Show\NoBankLeader.php
 */

namespace App\Admin\Actions\Show;

use App\Models\Order;
use Illuminate\Http\Request;
use Dcat\Admin\Actions\Action;
use Dcat\Admin\Actions\Response;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\NotifyController;

class OrderNotifyUrl extends Action
{
    /**
     * @return string
     * 手动回调
     */
    protected $title = '<i class="fa fa-undo"></i> 重发回调';

    protected $modalId = 'show-current6166-user';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle()
    {
        // $arr = explode(',', $this->getKey());
        // $id = isset($arr[0]) ? $arr[0] : 0;
        // $recommend_id = isset($arr[1]) ? $arr[1] : 0;

        $id = $this->getKey();

        try {
            //code...

            $data = Order::where('id', $id)->with('user')->whereHas('user', function ($query) {
                $query->where('status', 1);
            })->get()->toArray();

            foreach ($data as $k => $v) {
                //
                Log::channel('user_notify')->info('>>>>>>>>收款手动回调订单开始>>>>>>>>');

                if ($v['status'] == 2 || $v['status'] == 5) {
                    $result = 'success';
                } else {
                    // 回调下游的支付状态
                    $result = 'error';
                }
                // 用户回调数据
                $getData = [
                    'batch_no' => $v['batch_no'],
                    'amount' => $v['amount'],
                    // 'amount_san' => $v['amount_san'],
                    // 'result' => $result,
                    'status' => $v['status'],
                    'agent_id' => $v['user_id'],
                    'order_id' => $v['order_id'],
                    // 'meta_data' => $v['meta_data'],
                    'pay_at' => $v['pay_at'],
                ];

                // 用户回调地址
                $url = $v['notify_url'];
                $id = $v['id'];
                $num = $v['user_notify_num'];
                Log::channel('user_notify')->info('处理数据', [$getData, $url, $id, $num]);
                $aa = NotifyController::orderNotify(
                    $getData,
                    $id,
                    $url,
                    $num,
                    $v['user']['key']
                );
                Log::channel('user_notify')->info('商户响应回调', [$aa]);
            }
            Log::channel('user_notify')->info('<<<<<<<<收款手动回调订单结束<<<<<<<<');
            return $this->response()->success('重发回调成功.')->refresh();
        } catch (\Throwable $th) {
            //throw $th;
            // Log::channel('user_notify')->info($th->getMessage());
            Log::channel('user_notify')->info(
                '响应错误',
                [$th->getMessage()]
            );
            Log::channel('user_notify')->info('<<<<<<<<收款手动回调订单结束<<<<<<<<');
            return $this->response()->error('重发回调失败.');
        }
    }

    public function confirm()
    {
        return ['你确定要重发回调吗？'];
    }
}
