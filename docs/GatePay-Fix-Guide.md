# GatePay API 错误修复指南

## 问题分析

你遇到的错误：
```json
{
    "success": true,
    "status": 200,
    "data": {
        "status": "FAIL",
        "code": 400001,
        "label": "INVALID_REQUEST",
        "errorMessage": "Parameter format is wrong or parameter transferring doesn't follow the rules"
    }
}
```

这个错误表示**参数格式错误**，根据官方文档分析，主要问题在于：

## 修复内容

### 1. **订单数据结构修正**

**之前的错误格式：**
```php
$orderData = [
    'amount' => '100.00',
    'currency' => 'USDT',
    'order_id' => 'TEST_' . time(),
    // ... 其他参数格式不正确
];
```

**修正后的正确格式：**
```php
$orderData = [
    'merchantTradeNo' => 'ORDER_' . time() . '_' . rand(1000, 9999),  // 商户交易号
    'currency' => 'USDT',                                              // 订单币种
    'orderAmount' => '10.50',                                          // 订单金额（字符串格式）
    'env' => [                                                         // 环境信息（对象格式）
        'terminalType' => 'WEB'
    ],
    'goods' => [                                                       // 商品信息（对象格式）
        'goodsName' => '测试商品',
        'goodsDetail' => '商品详情描述'
    ],
    'orderExpireTime' => $this->getMilliTimestampAfterNMinutes(10),   // 过期时间（毫秒时间戳）
    'returnUrl' => 'https://your-domain.com/success',                  // 成功回调URL
    'cancelUrl' => 'https://your-domain.com/cancel',                   // 取消回调URL
    'merchantUserId' => (int)$this->config['userId'],                  // 商户用户ID（整数）
    'chain' => 'ETH',                                                  // 链名称
    'fullCurrType' => 'USDT_ETH',                                      // 完整币种类型
    'channelId' => 'TEST_CHANNEL_001'                                  // 渠道ID（可选）
];
```

### 2. **关键修复点**

#### ✅ **参数名称修正**
- `order_id` → `merchantTradeNo`
- `amount` → `orderAmount`
- 添加必需的 `env` 和 `goods` 对象

#### ✅ **数据类型修正**
- `merchantUserId`: 必须是整数类型
- `orderExpireTime`: 必须是毫秒时间戳
- `env` 和 `goods`: 必须是对象，不是字符串

#### ✅ **必需参数补全**
根据官方文档，以下参数是必需的：
- `merchantTradeNo` - 商户交易号
- `currency` - 订单币种
- `orderAmount` - 订单金额
- `env` - 环境信息
- `goods` - 商品信息
- `orderExpireTime` - 过期时间
- `merchantUserId` - 商户用户ID
- `chain` - 链名称
- `fullCurrType` - 完整币种类型

### 3. **测试方法**

现在你可以通过以下URL进行测试：

#### 🧪 **基础功能测试**
```
GET /gatepay/test                    # 签名功能测试
GET /gatepay/test-api               # 综合API测试
GET /gatepay/test-create-order      # 专门测试创建订单
```

#### 🔍 **单独功能测试**
```
GET /gatepay/chains/USDT            # 获取USDT支持的链列表
GET /gatepay/balance                # 获取账户余额
GET /gatepay/order/{orderId}        # 查询订单状态
```

### 4. **配置检查**

确保在 `GatePayController` 构造函数中配置正确：

```php
$this->config = [
    'url' => 'https://openplatform.gateapi.io',
    'ClientId' => 'your-real-client-id',        // 替换为真实的ClientId
    'secretKey' => 'your-real-secret-key',      // 替换为真实的密钥
    'userId' => 'your-real-user-id'             // 替换为真实的用户ID
];
```

### 5. **调试信息**

新版本包含完整的日志记录：

```php
// 查看Laravel日志
tail -f storage/logs/laravel.log

// 或者在代码中查看
Log::info('GatePay API 请求', $requestData);
Log::info('GatePay API 响应', $responseData);
```

### 6. **常见错误排查**

#### 🚫 **错误400001 - 参数格式错误**
- 检查所有必需参数是否存在
- 确认数据类型正确（整数、字符串、对象）
- 验证时间戳格式（毫秒）

#### 🚫 **错误400002 - 签名不合法**
- 检查ClientId和secretKey是否正确
- 确认签名算法实现正确
- 验证时间戳是否在有效范围内

#### 🚫 **错误400003 - 时间戳不正确**
- 确保使用毫秒时间戳
- 检查过期时间是否晚于当前时间

### 7. **成功响应示例**

正确的创建订单响应应该是：

```json
{
    "success": true,
    "status": 200,
    "data": {
        "status": "SUCCESS",
        "code": "000000",
        "errorMessage": "",
        "data": {
            "prepayId": "47656656276819968",
            "terminalType": "WEB",
            "expireTime": 1673410179000,
            "chain": {
                "chain_type": "ETH",
                "address": "******************************************"
            }
        }
    }
}
```

### 8. **下一步操作**

1. 访问 `/gatepay/test-create-order` 测试修正后的订单创建
2. 检查日志文件查看详细的请求和响应信息
3. 根据返回的地址信息进行支付测试
4. 实现订单状态查询和回调处理

现在参数格式已经完全符合官方文档要求，应该能够成功创建订单了！
