<?php

/**
 * Order表定时回调
 */
namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Order;
use App\Models\Matching;
use App\Models\PayChannel;
use App\Models\MatchingOrder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\NotifyController;
use App\Http\Controllers\alipay\AliPayController;
use App\Http\Controllers\Api\MatchingOrderController;

class OrderTimeOut extends Command
{
    protected $signature = 'order:timeout';
    protected $description = 'Order订单超时处理失败';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        while (true) {
            self::do();
        }
    }

    public static function do()
    {
        try {
            //code...
            // 默认5分钟
            $overtime = 300;
            $overtime = -$overtime;
            $data = Order::where('status', 1)
                ->where('user_status', 'error')
                ->where('created_at', '<=', Carbon::now()->addSeconds($overtime))
                ->with([
                    'channel' => function ($query) {
                        $query->select('id', 'channel_type');
                    }
                ])
                ->whereHas('channel', function ($query) {
                    $query->whereIn('channel_type', [PayChannel::ALIPAY_TYPE]);
                })
                ->orderBy('created_at')->first(['order_id', 'pay_channel_id']);

            if ($data) {
                //
                $data = $data->toArray();
                $arr = [
                    'orderNo' => $data['order_id'],
                    'tradeNo' => '',
                    'status' => 4,
                    'retMsg' => '超时未支付',
                ];
                $log = 'order_notify';
                Log::channel($log)->info('收款回调数据', [$arr]);
                Log::channel($log)->info('收款回调--', [$data['order_id'], 4]);
                // (new AliPayController())->close($data['pay_channel_id'], $data['order_id']);
                (new NotifyController())->orderNoticePayHandle($arr, 'huifu');
            } else {
                // 没有核实订单就睡眠10秒
                sleep(20);
            }
        } catch (\Throwable $th) {
            //throw $th;
            Log::channel('order_notify')->info($th->getMessage());
        }
    }
}
