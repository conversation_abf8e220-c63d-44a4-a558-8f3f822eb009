<?php

namespace App\Http\Controllers\GatePay;

use QL\QueryList;
use Carbon\Carbon;
use Yansongda\Pay\Pay;
use App\Models\PayChannel;
use App\Models\PaySubject;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Http;
use App\Http\Controllers\BaseController;
use Symfony\Component\HttpFoundation\Response;
use Yansongda\Pay\Plugin\Alipay\Fund\AccountQueryPlugin;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class GatePayController extends BaseController
{
    private $config;
    /**
     * 签名工具类常量
     */
    private const HMAC_SHA512 = 'sha512';
    public const INVALID_SIGNATURE = 'invalid signature';


    public function __construct()
    {
        $this->config = [
            'url' => 'https://openplatform.gateapi.io',
            'ClientId' => 'rnyQeYwjVgwTsnPJ',
            'secretKey' => 'JX7BzFrIH2ZA-vR2qJneTSmrf_LxdMkS960PgKHQsqE=',
            'userId' => '********'
        ];
    }



    /**
     * 生成签名（原Java方法：verifySignature）
     * @param string $timestamp 时间戳
     * @param string $nonce     随机字符串
     * @param string $body      请求体
     * @param string $secretKey 密钥
     * @return string 签名字符串
     */
    public function verifySignature($timestamp, $nonce, $body, $secretKey)
    {
        $payload = sprintf("%s\n%s\n%s\n", $timestamp, $nonce, $body);
        return $this->sign($payload, $secretKey);
    }

    /**
     * 对数据进行签名
     * @param string $signingData 要签名的数据
     * @param string $key         密钥
     * @return string 签名字符串
     */
    public function sign($signingData, $key)
    {
        try {
            $signature = hash_hmac(self::HMAC_SHA512, $signingData, $key, true);
            return $this->byteArrayToHexString($signature);
        } catch (\Exception $e) {
            throw new \RuntimeException("生成签名时出错: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * 将十六进制字符串转换为字节数组
     * @param string $hexString 十六进制字符串
     * @return string 字节数组
     */
    private function hexStringToByteArray($hexString)
    {
        if (strlen($hexString) % 2 !== 0) {
            throw new \InvalidArgumentException("十六进制字符串长度必须为偶数");
        }
        return hex2bin($hexString);
    }

    /**
     * 将字节数组转换为十六进制字符串
     * @param string $bytes 字节数组
     * @return string 十六进制字符串
     */
    private function byteArrayToHexString($bytes)
    {
        return bin2hex($bytes);
    }

    /**
     * 常量时间比较两个字节数组是否相等，防止时间侧信道攻击
     * @param string $a 第一个字节数组
     * @param string $b 第二个字节数组
     * @return bool 如果两个字节数组相等返回true，否则返回false
     */
    private function constantTimeEquals($a, $b)
    {
        if (strlen($a) !== strlen($b)) {
            return false;
        }

        $result = 0;
        $length = strlen($a);
        for ($i = 0; $i < $length; $i++) {
            $result |= ord($a[$i]) ^ ord($b[$i]);
        }
        return $result === 0;
    }

    /**
     * 验证签名是否有效
     * @param string $signingData 签名数据
     * @param string $signature   签名
     * @param string $key         密钥
     * @return string|null 如果签名有效返回null，否则返回错误信息
     */
    public function verify($signingData, $signature, $key)
    {
        try {
            $decodedSignature = $this->hexStringToByteArray($signature);
            $calculatedSignature = hash_hmac(self::HMAC_SHA512, $signingData, $key, true);

            if (!$this->constantTimeEquals($decodedSignature, $calculatedSignature)) {
                return self::INVALID_SIGNATURE;
            }

            return null;
        } catch (\Exception $e) {
            return self::INVALID_SIGNATURE;
        }
    }

    /**
     * 生成签名（Base64编码版本）
     * @param string $timestamp 时间戳
     * @param string $nonce     随机字符串
     * @param string $body      请求体
     * @param string $secretKey 密钥
     * @return string Base64编码的签名字符串
     */
    public function generateSignature($timestamp, $nonce, $body, $secretKey)
    {
        try {
            // 连接 timestamp, nonce 和 body
            $message = $timestamp . $nonce . $body;

            // 使用 HMAC-SHA512 计算签名
            $hash = hash_hmac(self::HMAC_SHA512, $message, $secretKey, true);

            // 使用 Base64 编码
            return base64_encode($hash);
        } catch (\Exception $e) {
            throw new \RuntimeException("生成签名失败: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * 验证Base64编码的签名
     * @param string $timestamp 时间戳
     * @param string $nonce     随机字符串
     * @param string $body      请求体
     * @param string $signature Base64编码的签名
     * @param string $secretKey 密钥
     * @return bool 签名是否有效
     */
    public function verifyBase64Signature($timestamp, $nonce, $body, $signature, $secretKey)
    {
        try {
            $expectedSignature = $this->generateSignature($timestamp, $nonce, $body, $secretKey);
            return $this->constantTimeEquals($signature, $expectedSignature);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 生成随机nonce字符串
     * @param int $length nonce长度，默认16
     * @return string 随机nonce字符串
     */
    public function generateNonce($length = 16)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $nonce = '';
        $charactersLength = strlen($characters);

        for ($i = 0; $i < $length; $i++) {
            $nonce .= $characters[random_int(0, $charactersLength - 1)];
        }

        return $nonce;
    }

    /**
     * 获取当前时间戳（毫秒）
     * @return string 毫秒时间戳
     */
    public function getCurrentTimestamp()
    {
        return (string) (time() * 1000);
    }

    /**
     * 发送GatePay API请求
     * @param string $method HTTP方法 (GET, POST, PUT, DELETE)
     * @param string $endpoint API端点 (例如: '/v1/pay/address/chains')
     * @param array $data 请求数据
     * @param array $queryParams 查询参数
     * @return array API响应结果
     */
    public function sendGatePayRequest($method = 'GET', $endpoint = '', $data = [], $queryParams = [])
    {
        try {
            // 生成请求参数
            $timestamp = $this->getCurrentTimestamp();
            $nonce = $this->generateNonce(10); // 生成10位随机nonce
            $body = empty($data) ? '' : json_encode($data);

            // 生成签名
            $signature = $this->verifySignature($timestamp, $nonce, $body, $this->config['secretKey']);

            // 构建完整URL
            $url = $this->config['url'] . $endpoint;
            if (!empty($queryParams)) {
                $url .= '?' . http_build_query($queryParams);
            }

            // 设置请求头
            $headers = [
                'Content-Type' => 'application/json',
                'X-GatePay-Timestamp' => $timestamp,
                'X-GatePay-Nonce' => $nonce,
                'X-GatePay-Signature' => $signature,
                'X-GatePay-Certificate-ClientId' => $this->config['ClientId'],
            ];

            // 记录请求日志
            Log::info('GatePay API 请求', [
                'method' => $method,
                'url' => $url,
                'headers' => $headers,
                'body' => $body
            ]);

            // 发送HTTP请求
            $response = Http::withHeaders($headers)->timeout(30);

            switch (strtoupper($method)) {
                case 'GET':
                    $result = $response->get($url);
                    break;
                case 'POST':
                    $result = $response->asJson()->post($url, $data);
                    break;
                case 'PUT':
                    $result = $response->put($url, $data);
                    break;
                case 'DELETE':
                    $result = $response->delete($url, $data);
                    break;
                default:
                    throw new \InvalidArgumentException("不支持的HTTP方法: $method");
            }

            // 记录响应日志
            Log::info('GatePay API 响应', [
                'status' => $result->status(),
                'response' => $result->json()
            ]);

            return [
                'success' => $result->successful(),
                'status' => $result->status(),
                'data' => $result->json(),
                // 'headers' => $result->headers()
            ];

        } catch (\Exception $e) {
            Log::error('GatePay API 请求失败', [
                'error' => $e->getMessage(),
                'method' => $method,
                'endpoint' => $endpoint
            ]);

            return [
                'success' => false,
                'status' => 500,
                'error' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 获取BTC支付地址链信息
     * @param string $currency 货币类型，默认BTC
     * @return array
     */
    public function getPayAddressChains($currency = 'BTC')
    {
        return $this->sendGatePayRequest('GET', '/v1/pay/address/chains', [], ['currency' => $currency]);
    }

    /**
     * 创建支付订单
     * @param array $orderData 订单数据
     * @return array
     */
    public function createPayOrder($orderData)
    {
        return $this->sendGatePayRequest('POST', '/v1/pay/address/create', $orderData);
    }

    /**
     * 查询订单状态
     * @param string $orderId 订单ID
     * @return array
     */
    public function getOrderStatus($orderId)
    {
        return $this->sendGatePayRequest('GET', "/v1/pay/address/query");
    }

    /**
     * 获取账户余额
     * @return array
     */
    public function getAccountBalance()
    {
        return $this->sendGatePayRequest('GET', '/v1/pay/balance/query');
    }

    public function index()
    {
        //
        $timestamp = "*************";
        $nonce = "abcd1234";
        $bodyArray = [
            'a' => 1,
            'b' => 2,
        ];
        $body = json_encode($bodyArray);
        $secretKey = $this->config['secretKey'];

        // 使用十六进制签名方法
        $signature = $this->verifySignature($timestamp, $nonce, $body, $secretKey);
        echo "十六进制签名: " . $signature;

        // 使用Base64签名方法
        $base64Signature = $this->generateSignature($timestamp, $nonce, $body, $secretKey);
        echo "<br>Base64签名: " . $base64Signature;

        // 验证Base64签名
        $isValid = $this->verifyBase64Signature($timestamp, $nonce, $body, $base64Signature, $secretKey);
        echo "<br>签名验证结果: " . ($isValid ? '有效' : '无效');

        // 生成随机nonce和当前时间戳示例
        $randomNonce = $this->generateNonce();
        $currentTimestamp = $this->getCurrentTimestamp();
        echo "<br>随机Nonce: " . $randomNonce;
        echo "<br>当前时间戳: " . $currentTimestamp;

        // 测试API请求
        echo "<br><br>=== API请求测试 ===<br>";
        $apiResult = $this->getPayAddressChains('USDT');
        echo "BTC地址链查询结果: <pre>" . json_encode($apiResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

        // 3. 测试创建支付订单（示例数据）
        echo "<h3>3. 创建支付订单（示例）</h3>";
        $orderData = [
            'merchantTradeNo' => '93840202212210025',
            'currency' => 'USDT',
            'orderAmount' => '100',
            'env' => json_encode(['terminalType' => 'WEB']),
            'goods' => json_encode(['goodsName' => '1333', 'goodsDetail' => 'yc'], JSON_UNESCAPED_UNICODE),
            'orderExpireTime' => self::getMilliTimestampAfterNMinutes(10),
            "returnUrl" => "www.test1.com",
            "cancelUrl" => "www.test2.com",
            'merchantUserId' => $this->config['userId'],
            'chain' => 'ETH',
            'fullCurrType' => 'USDT_ETH',

        ];
        // dd($orderData);
        $orderResult = $this->createPayOrder($orderData);
        echo "<pre>" . json_encode($orderResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";



        // 2. 测试获取账户余额
        // echo "<h3>2. 获取账户余额</h3>";
        // $balanceResult = $this->getAccountBalance();
        // echo "<pre>" . json_encode($balanceResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    }



    function getMilliTimestampAfterNMinutes($minutes)
    {
        // 获取当前时间戳（秒级）
        $currentTimestamp = time();

        // 计算N分钟后的秒级时间戳
        $futureTimestamp = $currentTimestamp + ($minutes * 60);

        // 获取当前时间的微秒部分
        $microtime = microtime(true);
        $microseconds = ($microtime - (int) $microtime) * 1000000;

        // 组合成毫秒级时间戳（秒级时间戳 * 1000 + 毫秒部分）
        $milliTimestamp = $futureTimestamp * 1000 + floor($microseconds / 1000);

        return (int) $milliTimestamp;
    }

    /**
     * 测试GatePay API请求的专用方法
     * @return \Illuminate\Http\JsonResponse
     */
    public function testGatePayApi()
    {
        try {
            // 1. 测试获取BTC支付地址链
            echo "<h3>1. 获取BTC支付地址链</h3>";
            $chainsResult = $this->getPayAddressChains('BTC');
            echo "<pre>" . json_encode($chainsResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

            // 2. 测试获取账户余额
            echo "<h3>2. 获取账户余额</h3>";
            $balanceResult = $this->getAccountBalance();
            echo "<pre>" . json_encode($balanceResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

            // 3. 测试创建支付订单（示例数据）
            echo "<h3>3. 创建支付订单（示例）</h3>";
            $orderData = [
                'amount' => '100.00',
                'currency' => 'USDT',
                'order_id' => 'TEST_' . time(),
                'callback_url' => 'https://your-domain.com/callback',
                'description' => '测试订单'
            ];
            $orderResult = $this->createPayOrder($orderData);
            echo "<pre>" . json_encode($orderResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

            return response()->json([
                'success' => true,
                'message' => 'API测试完成',
                'results' => [
                    'chains' => $chainsResult,
                    'balance' => $balanceResult,
                    'order' => $orderResult
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }































































    public function payment($arr = [], $channel)
    {
        //
        try {
            //code...
            $dataJson = json_decode($arr['dataJson'], true);
            self::configHandle($dataJson);

            $pay_channel_id = $arr['id'];
            $name = $arr['name'];
            // 收款限制额度
            $amount_limit = $arr['amount_limit'];
            $visitKey = 'user_channel_amount:' . date('d') . 'id:' . $pay_channel_id;
            $currentValue = Redis::get($visitKey);
            $amount = bcadd($currentValue, $arr['amount'], 2);
            if ($amount && $amount_limit && $amount <= $amount_limit) {
                //
                $data = $this->alipay($arr['order_id'], $arr['amount']);
                // 写入laravel日志
                Log::channel($channel)->info("通道{$pay_channel_id}:{$name}请求返回数据:", [$arr, $data['data']]);
                Log::channel($channel)->info("通道{$pay_channel_id}:{$name}请求返回数据:========== " . $data['msg']);

                if ($data['code'] == 200) {
                    Log::channel($channel)->info("通道{$pay_channel_id}:{$name}请求返回数据:========== 成功,返回数据");
                    return [
                        "code" => 200,
                        "pay_channel_id" => $pay_channel_id,
                        "pay_url" => $data['data'],
                    ];
                }
            } else {
                Log::channel($channel)->info("通道{$pay_channel_id}:{$name}请求返回数据:========== 通道额度限制");
            }
            Log::channel($channel)->info("通道{$pay_channel_id}:{$name}请求返回数据:========== 下单失败");
            return [
                "code" => 400,
            ];
        } catch (\Throwable $th) {
            //throw $th;
            return [
                "code" => 400,
            ];
        }
    }

    /**
     * 获取商户余额
     * @param mixed $pay_channel_id
     * available_amount  账户可用余额
     * freeze_amount｜当前账户不可用金额，单位
     * @return array
     */
    public function checkBalance($id, $dataJson)
    {
        try {
            self::configHandle($dataJson);
            $params = [
                'alipay_user_id' => $dataJson['alipay_user_id'], // 支付宝会员ID (这里填写商家号)
                'account_type' => 'ACCTRANS_ACCOUNT', // 查询的账号类型
            ];
            $allPlugins = Pay::alipay()->mergeCommonPlugins([AccountQueryPlugin::class]);
            $response = Pay::alipay()->pay($allPlugins, $params)->toArray();
            if ($response['code'] == 10000) {
                PayChannel::where('id', $id)->update(['avl_bal' => $response['available_amount']]);
                return $response;
            }
            return [
                'available_amount' => 0,
                'freeze_amount' => 0,
            ];
        } catch (\Throwable $th) {
            return [
                'available_amount' => 0,
                'freeze_amount' => 0,
            ];
        }
    }

    /**
     * 重新构建支付宝参数
     * @param mixed $arr
     * @return void
     */
    public function configHandle($arr = [])
    {

        $this->config['alipay']['default']['app_id'] = $arr['app_id'];
        $this->config['alipay']['default']['app_secret_cert'] = $arr['private_key'];
        $this->config['alipay']['default']['app_public_cert_path'] = storage_path('app/public/') . $arr['app_cert_public_key'];
        $this->config['alipay']['default']['alipay_public_cert_path'] = storage_path('app/public/') . $arr['ali_public_key'];
        $this->config['alipay']['default']['alipay_root_cert_path'] = storage_path('app/public/') . $arr['alipay_root_cert'];
        // 初始化
        Pay::config($this->config);
    }

    /**
     * 支付宝收款
     * @param int $payId
     * @param string $title
     * @param string $orderNo
     * @param string $amount
     * @return array
     */
    public static function alipay(string $orderNo, string $amount): array
    {
        try {
            //code...
            // 交易有效期
            $currentTime = Carbon::now();
            // 5分钟后过期
            $extendedTime = $currentTime->addMinutes(5);
            $formattedExtendedTime = $extendedTime->format('Y-m-d H:i:s');

            $order = [
                'out_trade_no' => $orderNo,
                'total_amount' => $amount,
                'subject' => "订单{$orderNo}支付",
                'http_method' => 'POST',
                // 设置订单绝对超时时间
                'time_expire' => $formattedExtendedTime
            ];
            $alipay = Pay::alipay()->wap($order);
            $html = $alipay->getBody();
            $hiddenFields = (new QueryList)->html($html)->find('input[type="hidden"]')->map(function ($item) use (&$arr) {
                $arr[$item->attr('name')] = $item->attr('value');
                return array_merge($arr, [$item->attr('name') => $item->attr('value')]);
            })->last();
            return [
                'code' => 200,
                'data' => 'https://openapi.alipay.com/gateway.do?' . http_build_query($hiddenFields),
                'msg' => '成功',
            ];
        } catch (\Throwable $th) {
            return [
                'code' => 400,
                'msg' => '异常',
            ];
        }
    }

    /**
     * 商家批量转账给支付宝用户
     * @return
     */
    public function TransferPlugin($arr = ['pay_channel_id' => 9])
    {
        try {
            //code...
            $dataJson = PayChannel::dataJson($arr['pay_channel_id']);
            self::configHandle($dataJson);
            //提现到支付宝
            $aliOrder = [
                'out_biz_no' => $arr['recharge_list_order'],
                'product_code' => 'TRANS_ACCOUNT_NO_PWD',
                'trans_amount' => $arr['amount'],
                'biz_scene' => 'DIRECT_TRANSFER',
                'remark' => '支付宝提现',
                'payee_info' => [
                    'identity' => $arr["pan"], //提现人支付宝
                    'identity_type' => 'ALIPAY_LOGON_ID',
                    'name' => $arr["cardHolderName"] //提现人真实姓名
                ]
            ];
            $result = Pay::alipay()->transfer($aliOrder)->toArray();
            Log::channel('recharge')->info("支付宝提现请求返回数据:", [$aliOrder, $result]);
            if ($result['code'] == 10000) {
                // "code" => "10000"
                // "msg" => "Success"
                // "order_id" => "20250117020070011560910013455948"
                // "out_biz_no" => "2501171625280975057467"
                // "pay_fund_order_id" => "20250117020070011560910013455948"
                // "status" => "SUCCESS"
                // "trans_date" => "2025-01-17 16:25:29"
                return [
                    'status' => 200,
                    'message' => '提现成功',
                ];
            }
            return [
                'status' => 400,
                'message' => $result['sub_msg'],
            ];
        } catch (\Throwable $th) {
            //throw $th;
            return [
                'status' => 400,
                'message' => $th->getMessage(),
            ];
        }
    }

    /**
     * 6分钟超时关闭订单
     * @return void
     */
    public function close($pay_channel_id, $out_trade_no)
    {
        $dataJson = PayChannel::dataJson($pay_channel_id);
        self::configHandle($dataJson);

        $result = Pay::alipay()->close([
            'out_trade_no' => $out_trade_no,
            // '_action' => 'web', // 默认值，关闭网页订单
            // '_action' => 'mini', // 关闭小程序订单
            // '_action' => 'scan', // 关闭扫码订单
            // '_action' => 'pos', // 关闭刷卡订单
            '_action' => 'h5', // 关闭 H5 订单
        ])->toArray();

        Log::channel('order_notify')->info('关闭订单--', [$result, $out_trade_no]);
        //     "msg" => "Business Failed"
        // "code" => "40004"
        // "sub_msg" => "交易不存在"
        // "sub_code" => "ACQ.TRADE_NOT_EXIST"
        // dd($result, $result->toArray());
    }
}
