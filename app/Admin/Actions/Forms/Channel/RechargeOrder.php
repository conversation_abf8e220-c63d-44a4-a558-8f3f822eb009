<?php

namespace App\Admin\Actions\Forms\Channel;

use App\Models\HFB;
use App\Models\User;
use Dcat\Admin\Admin;
use App\Models\PayChannel;
use App\Models\RechargeFail;
use Dcat\Admin\Widgets\Form;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use App\Http\Controllers\OrderController;
use App\Admin\Actions\Imports\RechargeImport;

class RechargeOrder extends Form
{
    public function handle(array $input)
    {
        try {
            //上传文件位置，这里默认是在storage中，如有修改请对应替换
            $batch_amt = $input['batch_amt'] ?? 0;
            $num = $input['num'] ?? 0;
            $idcard = $input['idcard'] ?? '';
            $name = $input['name'] ?? '';
            $notify_url = $input['notify_url'] ?? '';

            // 合并新数据到请求对象
            $order_id = generateOrderNumber();
            request()->merge([
                'agent_id' => $input['user_id'],
                'batch_no' => $order_id,
                'amount' => $batch_amt,
                'card_name' => $name,
                'cert_no' => $idcard,
                'bank_code' => $num,
                'notify_url' => $notify_url,
            ]);

            $tradePay = (new OrderController())->tradePay(request());
            $bodydata = $tradePay->getData(true);

            $code = $bodydata['code'] ?? 400;
            $message = $bodydata['message'] ?? '申请失败';
            if ($code == 200) {
                //
                return $this->response()->success($message)->refresh();
            } else {
                return $this->response()->error($message);
            }

        } catch (\Exception $e) {
            return $this->response()->error($e->getMessage());
        }
    }


    // 上传表单
    public function form()
    {
        $this->html('<h4><font color="red" style="font-weight:bold">一旦提交，订单将不能撤回，请自行仔细核对订单信息，如有输错选错等情况造成的损失，后果自负！</font></h4>');
        $this->select('user_id', '下发商户')->options(User::where('status', 1)->pluck('name', 'agent_id'))->required()->help('请选择下发商户');
        $this->text('batch_amt', '金额');
        $this->text('name', '收款人')->required()->value('张三')->help('请填写真实姓名');
        $this->text('num', '收款账号')->required();
        $this->text('idcard', '身份证ID')->value('123345')->help('可以不填写');
        $this->url('notify_url', '回调地址')->default(env('APP_URL') . '/api/user')->help('有自己的回调地址请编辑，没有地址请勿编辑')->required();

    }
}
