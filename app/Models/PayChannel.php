<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class PayChannel extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    /**
     * 汇付天下
     * @var int
     */
    const HUIFU_TYPE = 1;

    /**
     * 快钱支付
     * @var int
     */
    const KUAIQIAN_TYPE = 2;

    /**
     * 支付宝官方
     * @var int
     */
    const ALIPAY_TYPE = 3;

    /**
     * 安全发 cyf.xzs-branch.com
     * @var int
     */
    const AQFPAY_TYPE = 4;

    protected $table = 'pay_channel';

    public $channel_type_code = [self::HUIFU_TYPE => '汇付天下', self::KUAIQIAN_TYPE => '快钱支付', self::ALIPAY_TYPE => '支付宝', self::AQFPAY_TYPE => '安全发'];

    public static function dataJson($id = 0)
    {
        return json_decode(self::where('id', $id)->value('dataJson'), 1);
    }

    /**
     * 查询出后台用户未绑定的商户
     *
     * @param [type] $query
     * @return void
     */
    public function scopeNotInUser($query)
    {
        $arr = [];
        AdminUser::where('pay_channel_id', '<>', null)->pluck('pay_channel_id')->map(function ($item) use (&$arr) {
            //
            $arr = array_merge($arr, explode(",", $item));
        });
        return $query->whereNotIn('id', $arr);
    }

    /**
     * 编辑当前后台用户并排除当前数据不进行过滤
     *
     * @param [type] $query
     * @param [type] $id
     * @return void
     */
    public function scopeFilterIdNotInUser($query, $id)
    {
        $arr = [];
        AdminUser::where('id', '<>', $id)->where('pay_channel_id', '<>', null)->pluck('pay_channel_id')->map(function ($item) use (&$arr) {
            //
            $arr = array_merge($arr, explode(",", $item));
        });
        return $query->whereNotIn('id', $arr);
    }
}
