<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Form;
use App\Models\Users;
use Dcat\Admin\Admin;
use Illuminate\Http\Request;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Models\Administrator;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Cache;
use Dcat\Admin\Traits\HasFormResponse;
use Illuminate\Support\Facades\Validator;
use Dcat\Admin\Http\Controllers\AuthController as BaseAuthController;

class AuthController extends BaseAuthController
{
    use HasFormResponse;

    /**
     * @var string
     */
    protected $view = 'admin::pages.login';


    /**
     * Handle a login request.
     *
     * @param  Request  $request
     * @return mixed
     */
    public function postLogin(Request $request)
    {
        $credentials = $request->only([$this->username(), 'password']);
        $remember = (bool) $request->input('remember', false);

        /** @var \Illuminate\Validation\Validator $validator */
        $validator = Validator::make($credentials, [
            $this->username() => 'required',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorsResponse($validator);
        }

        if ($this->guard()->attempt($credentials, $remember)) {
            // 商户IP白名单
            if (Admin::user()->isRole('administrator') && env('ADMIN_LOGIN_AUTH')) {
                // 联系管理员设置IP白名单
                $this->guard()->logout();
                return $this->validationErrorsResponse([
                    $this->username() => $this->getFailedLoginMessage(),
                ]);
            }

            return $this->sendLoginResponse($request);
        }

        return $this->validationErrorsResponse([
            $this->username() => $this->getFailedLoginMessage(),
        ]);
    }
}
