<?php
/*
 * @Author: your name
 * @Date: 2021-03-04 14:16:23
 * @LastEditTime: 2021-07-07 10:58:27
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \huoke\app\Admin\Actions\Forms\Order\Assistant.php
 */

namespace App\Admin\Actions\Forms\User;

use Dcat\Admin\Admin;
use App\Models\Message;
use App\Models\PayChannel;
use Dcat\Admin\Widgets\Form;
use App\Models\User;
use App\Services\UserUpAmount;
use App\Models\PayChannelOrder;
use Dcat\Admin\Traits\LazyWidget;
use Illuminate\Support\Facades\DB;
use App\Models\UsersRechargeAmount;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Dcat\Admin\Contracts\LazyRenderable;

class MoneyChargeTwo extends Form implements LazyRenderable
{
    use LazyWidget;

    // 增加一个自定义属性保存用户ID
    protected $id;
    protected $recharge_point;
    protected $pay_channel_id;

    // 构造方法的参数必须设置默认值
    public function __construct($id = null, $recharge_point = 0, $pay_channel_id = 0)
    {
        $this->id = $id;
        $this->recharge_point = $recharge_point;
        $this->pay_channel_id = $pay_channel_id;
        parent::__construct();
    }

    /**
     * 处理请求
     *
     * @param array $input
     */
    public function handle(array $input)
    {
        DB::beginTransaction();
        try {
            //code...
            $id = $input['id'] ?? null;
            $money = $input['money'] ?? 0;
            $type = $input['type'] ?? 2;
            Log::channel('money_log')->info('<<<<<<<<<<<<<<<<<<<<');
            Log::channel('money_log')->info('用户充值');

            $user = User::where('id', $id)->firstOrFail();
            $mone = $user->recharge_amount;
            $user->increment('recharge_amount', $money);

            if ($type == 5) {
                // 授信
                $user->increment('credit_amount', $money);
                if ($user->credit_amount < 0) {
                    // 
                    return $this->response()->error('扣除授信额度不能大于授信额度！');
                }
                if ($user->recharge_amount < 0) {
                    // 
                    return $this->response()->error('扣除授信额度不能大于可用余额！');
                }

            }
            $mtwo = $user->recharge_amount;
            //
            Log::channel('money_log')->info('数据', [
                'id' => $id,
                'status' => '成功',
                '提交金额' => $money,
                '实际到账金额' => $money,
                '充值前金额' => $mone,
                '充值后金额' => $mtwo,
                '下游手续费' => 0,
                '上游手续费' => 0,
            ]);

            // 写入商户账变明细日志
            $agent_id = User::where('id', $id)->value('agent_id');
            UserUpAmount::AddUserAccountChangeRecord($type, $agent_id, $money, 0, $type, $mone, $mtwo);
            $user->save();

            DB::commit();
            Redis::del('balanceWarning' . $agent_id);
            Log::channel('money_log')->info('结束');
            Log::channel('money_log')->info('>>>>>>>>>>>>>>>>');
            return $this->response()->success('充值成功')->refresh();
        } catch (\Throwable $th) {
            //throw $th;
            DB::rollBack();
            Log::channel('money_log')->info('结束', [$th->getMessage()]);
            Log::channel('money_log')->info('>>>>>>>>>>>>>>>>');
            return $this->response()->error('充值失败');
        }
    }

    /**
     * 创建表单
     *
     * @return void
     */
    public function form()
    {
        $id = $this->id;
        $recharge_point = $this->recharge_point;
        $pay_channel_id = $this->pay_channel_id;
        $this->radio('type', '余额类型')->options([2 => '充值', 5 => '授信'])->value(2);
        $this->number('money', '充值余额金额(元)')->default(1)->help('充值金额最小金额1元')->attribute(['id' => 'money-' . $id]);

        // 设置隐藏表单，传递用户id
        $this->hidden('id')->default($id);
        $this->hidden('recharge_point')->default($recharge_point);
        $this->hidden('pay_channel_id')->default($pay_channel_id);
    }
}
