<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class UsersBank extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'users_bank';

    protected $fillable = [
        'user_id',
        'num',
        'name',
        'idcard',
        'mobile_no',
        'cert_begin_date',
        'msg',
        'status',
        'attestation',
        'bank_type',
        'created_at',
    ];
}
