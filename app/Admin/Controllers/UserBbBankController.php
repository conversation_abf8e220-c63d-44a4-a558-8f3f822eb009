<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use App\Models\PayChannel;
use App\Models\UserBbBank;
use App\Models\User;
use Dcat\Admin\Http\Controllers\AdminController;

class UserBbBankController extends AdminController
{
    protected $PayChannel;
    protected $user;

    public function __construct()
    {
        $this->PayChannel = PayChannel::pluck('name', 'id')->toArray();
        $this->user = User::pluck('name', 'agent_id')->toArray();
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UserBbBank(), function (Grid $grid) {
            $grid->model()->orderByDesc('id');
            $grid->column('id')->sortable();
            $grid->column('user_id', '商户号');
            $grid->column('agent_name', '商户名称')->display(function () {
                return $this->user_id;
            })->using($this->user);
            $grid->column('pay_channel_id')->display(function ($value) {
                return PayChannel::where('id', $value)->value('name');
            });
            $grid->column('num');
            $grid->column('name');
            $grid->column('idcard');
            $grid->column('status', '认证')->using([1 => '未认证', 2 => '已认证', 3 => '认证失败'])->label([1 => 'warning', 2 => 'success', 3 => 'danger']);
            $grid->column('attestation', '绑定')->using([1 => '未绑定', 2 => '已绑定', 3 => '绑定失败'])->label([1 => 'warning', 2 => 'success', 3 => 'danger']);
            $grid->column('amount', '余额')->setAttributes(['style' => 'color:#2d9e2bd6'])->sortable();
            $grid->column('msg', '信息');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                // 展开过滤器
                $filter->expand();
                // 更改为 panel 布局
                $filter->panel();
                $filter->equal('user_id', '商户名称')->width(3)->select($this->user);
                $filter->equal('pay_channel_id')->width(3)->select($this->PayChannel);
            });

            $grid->disableCreateButton();
            $grid->disableViewButton();
            $grid->disableEditButton();
            // $grid->disableDeleteButton();
            $grid->disableRowSelector();
            // 显示快捷编辑按钮
            // $grid->showQuickEditButton();
            // 设置弹窗宽高，默认值为 '700px', '670px'
            $grid->setDialogFormDimensions('50%', '70%');
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UserBbBank(), function (Show $show) {
            $show->field('id');
            $show->field('agent_id');
            $show->field('pay_channel_id');
            $show->field('num');
            $show->field('name');
            $show->field('idcard');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UserBbBank(), function (Form $form) {
            $form->display('id');
            $form->select('agent_id')->options($this->user)->required();
            $form->select('pay_channel_id')->options($this->PayChannel)->required();
            $form->text('num')->required();
            $form->text('name');
            $form->text('idcard');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
