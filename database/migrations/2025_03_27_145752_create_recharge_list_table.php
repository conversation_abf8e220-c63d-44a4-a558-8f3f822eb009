<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateRechargeListTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('recharge_list', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('agent_id')->nullable()->comment('上游商户号');
            $table->integer('bb_bank_id')->nullable()->comment('提现卡ID');
            $table->integer('recharge_id')->nullable()->comment('提现id');
            $table->string('recharge_list_order')->nullable()->comment('提现子单号');
            $table->decimal('amount')->nullable()->comment('金额');
            $table->decimal('amount_san')->nullable()->comment('实际金额');
            $table->string('pay_channel_id')->nullable()->comment('通道ID');
            $table->integer('status')->default('1')->nullable()->comment('1 未转账 2成功 3失败');
            $table->integer('qx_status')->default('1')->nullable()->comment('1 未取现2成功 3失败 4取现中');
            $table->timestamp('pay_at')->nullable()->comment('付款时间');
            $table->longText('error_msg')->nullable()->comment('错误信息');
            $table->string('pdf_url')->nullable()->comment('回单');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('recharge_list');
    }
}
