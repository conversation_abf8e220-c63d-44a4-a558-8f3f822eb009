<?php

namespace App\Admin\Actions\Imports;

use App\Models\RechargeFail;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class FirstRechargeSheetImport implements ToCollection, WithStartRow, WithBatchInserts, WithChunkReading
{
    private $arr;
    private $agent_id;
    private $notify_url;
    private $pay_channel_id;

    public function __construct($arr = [], $agent_id = 0, $notify_url = '', $pay_channel_id = 1)
    {
        $this->arr = $arr;
        $this->agent_id = $agent_id;
        $this->notify_url = $notify_url;
        $this->pay_channel_id = $pay_channel_id;
    }

    public function collection(Collection $rows)
    {
        foreach ($this->arr as $k => $v) {
            //
            $batch_amt = trim($v[1]);
            $num = trim($v[2]);
            $name = trim($v[3]);
            $idcard = trim($v[4]);

            // 数据去重
            $user = RechargeFail::where([
                'batch_no' => $v[0],
                'user_id' => $this->agent_id
            ])->exists();
            if (!$user) {
                RechargeFail::insert([
                    'batch_no' => $v[0],
                    'user_id' => $this->agent_id,
                    'batch_amt' => $batch_amt,
                    'notify_url' => $this->notify_url,
                    'num' => $num,
                    'name' => $name,
                    'idcard' => $idcard,
                    'status' => 1,
                    'created_at' => now(),
                ]);
            }
        }
    }



    /**
     * 从第几行开始处理数据 就是不处理标题
     * @return int
     */
    public function startRow(): int
    {
        return 2;
    }

    public function batchSize(): int
    {
        return 450;
    }

    public function chunkSize(): int
    {
        return 450;
    }
}
