<?php

/**
 * 获取支付通道余额
 */

namespace App\Admin\Actions\Show;

use App\Models\PayChannel;
use Illuminate\Http\Request;
use Dcat\Admin\Actions\Action;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Contracts\LazyRenderable;
use App\Http\Controllers\HuiFuController;
use App\Http\Controllers\alipay\AliPayController;
use App\Http\Controllers\AqPay\PaySecurityController;
use App\Http\Controllers\kq\FirstCertificationController;

class GetChannelAmount extends Action implements LazyRenderable
{
    use LazyWidget;
    /**
     * @return string
     *
     */
    protected $title = '<i class="fa fa-tripadvisor"></i> 查看余额';

    protected $modalId = 'GetChannelAmount';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle()
    {
        $id = $this->getKey();
        try {
            $data = PayChannel::where('id', $id)->where('status', 1)->first(['channel_type', 'dataJson', 'pay_channel_url']);

            if ($data['channel_type'] == PayChannel::HUIFU_TYPE) {
                $balanceArr = (new HuiFuController())->tradeAcctPaymentBalanceQuery($id);
                return $this->response()->alert(true)->success('账户余额')->detail('可用余额：' . ($balanceArr['avl_bal']) . "<br>账户余额：" . $balanceArr['balance_amt']);
            }

            if ($data['channel_type'] == PayChannel::KUAIQIAN_TYPE) {
                $balanceArr = (new FirstCertificationController())->M0001($id);
                return $this->response()->alert(true)->success('账户余额')->detail('可用余额：' . ($balanceArr['avl_bal']));
            }

            if ($data['channel_type'] == PayChannel::ALIPAY_TYPE) {
                $dataJson = json_decode($data['dataJson'], true);
                $balanceArr = (new AliPayController())->checkBalance($id, $dataJson);
                return $this->response()->alert(true)->success('账户余额')->detail('可用余额：' . ($balanceArr['available_amount']));
            }

            if ($data['channel_type'] == PayChannel::AQFPAY_TYPE) {
                $avl_bal = PayChannel::where('id', $id)->value('avl_bal');
                return $this->response()->alert(true)->success('账户余额')->detail('可用余额：' . ($avl_bal));
            }
            return $this->response()->alert(true)->error('通道不存在');
        } catch (\Throwable $th) {
            //throw $th;
            return $this->response()->alert(true)->error('通道异常');
        }
    }

    public function confirm()
    {
        return [];
    }
}
