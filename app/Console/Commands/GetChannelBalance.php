<?php
/**
 * 获取通道余额并更新
 */
namespace App\Console\Commands;

use App\Models\PayChannel;
use Illuminate\Console\Command;
use App\Http\Controllers\HuiFuController;
use App\Http\Controllers\kq\FirstCertificationController;
# 加载 SDK 初始化文件
require_once base_path() . "/public/BsPaySdk/init.php";
# 加载 快钱 SDK 初始化文件
require_once base_path() . "/public/kq-php-sdk/index.php";
class GetChannelBalance extends Command
{

    protected $signature = 'channel:balance';
    protected $description = '获取通道余额并更新';

    public function handle()
    {
        while (true) {
            self::do();
        }
    }

    public function do()
    {
        $config = require(APP_PATH . '/config/MerchantConfig.php');

        $PayChannel = PayChannel::whereIn('channel_type', [1, 2])->get();
        foreach ($PayChannel as $key => $value) {
            if ($value->channel_type == 1) {
                (new HuiFuController())->tradeAcctPaymentBalanceQuery($value->id);
            }
            if ($value->channel_type == 2) {
                // (new FirstCertificationController())->M0001($value->id, $config);
            }
        }
        sleep(120);
    }
}
