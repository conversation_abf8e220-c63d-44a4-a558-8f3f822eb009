<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class RechargeList extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'recharge_list';

    static public function getPayChannelMap()
    {
        return PayChannel::pluck('name', 'id')->toArray();
    }

    static public function getStatusMap()
    {
        return [1 => '未转账', 2 => '已转账', 3 => '失败'];
    }

    static public function getQxStatusMap()
    {
        return [1 => '未取现', 2 => '已取现', 3 => '失败', 4 => '取现中'];
    }
}
