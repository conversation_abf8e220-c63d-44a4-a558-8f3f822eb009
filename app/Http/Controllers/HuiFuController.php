<?php
/*
 * @Author: your name
 * @Date: 2021-03-04 10:17:27
 * @LastEditTime: 2021-04-22 10:34:05
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \huoke\app\Http\Controllers\Api\AuthController.php
 */

namespace App\Http\Controllers;

use Exception;
use Carbon\Carbon;
use App\Models\Order;
use BsPaySdk\core\BsPay;
use App\Models\UsersBank;
use App\Models\PayChannel;
use App\Models\UserBbBank;
use BsPaySdk\core\BsPayClient;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use BsPaySdk\request\V2UserBusiOpenRequest;
use BsPaySdk\request\V2TradePaymentJspayRequest;
use BsPaySdk\request\V2UserBasicdataIndvRequest;
use BsPaySdk\request\V2TradeAcctpaymentPayRequest;
use BsPaySdk\request\V2TradePaymentScanpayQueryRequest;
use BsPaySdk\request\V2TradeSettlementEnchashmentRequest;
use BsPaySdk\request\V2TradeAcctpaymentBalanceQueryRequest;
// use App\Services\Huifu\V2TradePaymentJspayRequest;

class HuiFuController extends BaseController
{
    private $config;

    // public function __construct($id = 0)
    // {
    //     $config = [
    //         "sys_id" => "****************",
    //         "huifu_id" => "****************",
    //         "product_id" => "KAZX",
    //         "rsa_merch_private_key" => "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCG/i/OkPLfWcQKrRCW9Zpu2LPYj2VJPIpuQknS170UltBEb63Tk/yiPJ4qN47Qi5k0JPRoftMD7IOECmxtZhBgIj/FC+AIWJFsvqe1w32gyogHP/JAEw8LgPkCwMwCEeItnS1sj6smuMnB1Fvoc/4JHD7dUYyhBFCFpmOZhHzUP4y2fC53E+4Eb7kStSz+iCyex1rmwRy/psDU9oQyVekeJbW2G/pZdt3aB8/9l74e86MMtOmDXWz9hU7y+2y3od2sUzowNTMfmDFpFTqkqGDa5Hr7evAJdEK7fD6/cnYeHWJPBPaWNrM9w1owGnzZ5jKnlbHuB/sf4WH7PmLTgOBVAgMBAAECggEACQto2pc9npdeGjUEQokMItckHaNl7uU+hSqt1ZGc2o9MPvmYlO96Ysha9IsqZ3CjACplscaljsK/AQ7Nuvq2qCpDSYmFJioA/F89z2DcvvQy7wFq0r40KNooVPXj9VOLavD4BSkPF5YT9oeq7MUwxR1+XMfE+1jVqE7Ap52H25SZL7zyygaPsOfqT9dnRpxjF1cxl19YM3ILOJ1sii0G/yU2Fy9EephSVSok4fKciJ+ibR1ZqGQCPeefCMG8LjXTCOaxGSFVpdewfx/SRpZW7hnUmYPcAPL88bFq9hfo0Cj41XTdugiXQBgI4OuuiDRQGqReupivM9LZYWZC6QXNQQKBgQDbqnIylQKiWhD6ULPbelrqv/BmsTrX1jC4fvAWJirvwJ0PvEQO/tfC824C8FjZXaxDZ7XcQ58Z+pZTPGT/UZYn1kvg4crdury6dH+3azZfpHnbw4/tAegiKnbBTXNOMCXREORsyS9DB2GVPTqPvDWkSI45XaEziBOjqnUD2Yrl5QKBgQCdUlecNmilp7YRRJ27vXK2FJOew9dVIcQAIq68bXB1enBhn79V/qJQ2/ZScHH+3DQ/ehmIoRE/87mVQZc/SSDNDk4gW0ZRAtVhr7ZBJTy+5giEybPFPdN+cyZzRWdnW2kjYORcY+TKexotXllmNsSm6yhF2BZz19C0cr0cFqlpsQKBgQCUoafTge/eWoLSXHjWMNwDqwvG8HSYRl2A+KMK2ZiD0Rh05TZHzze+uWfepTH5IeDFWw86kqa8tUmx2FOeTl4DZoTBDQZvMUMyBlHEkc6guHGbuZ9RBtFDDOh9syIp7XopE/z7fsVz3TKyAFP3nanOFynktu20KIbhXxz+Fdxd4QKBgAInO12+/OWvivVnjAUizawiUfbtVUgsAyPBVcTsdoqVF26uog7KQmx65j8wnM7RMsHfqPPOyI6ohlS9phVOqe1EkdsthKPdNxB0ODliChfOzDUNEbHUa82iQ9d2DrlCbgj27Yk6MMp4rt4KGY0lmMF0HIQANiZ1wLyHiYEV6lGxAoGBAKTbRnXXAykLu0j2CsbbbKEl7xDPE6zM4K3USKj670KPBMZmDQ6CFRZoPYneMDBzGQb4NQL77tS63tkeTE531cjJq1SAzviB7RMmqNQ21+gtDs+nB7X3UAM0ZWDhdPS+JWO3FNPc2jxicKng411Wb9nRmz1Y0l0kxH+NA9jjyaMB",
    //         "rsa_huifu_public_key" => "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAi9NtaniV66Wuk9ES16NLmKaQpBTBbf76amSYrY8FRKXDB9/ZzVA5jQEh7BPpYnYx3aERYQqkpPgm34QQGPQ89/2MMO7ejSpvF08X8nFLvqbbKQ/+YA7sqbagypLN9fTx77CW/nIXXLRvpKqGNOn4Wt7x07Ke/S50rGPk46MAonXQ0skO01eeQiTjWZP6R14m4XR2hwSjxNuZr8Vt6oNwV5+RRmDD91JktFx0AO+KyMNxuBkFRXQ3gpFXQgZKPS5CgNcchhHbFlQKbc/PqGn7L9TPNaEoXhg635Vn1foxHWBPmgaoWhlJIkMSGdmcds9rRG3HkhZpNx7g9XZsNiT/8QIDAQAB",
    //         "wx_appid" => "wxc8f8c79ef11040dc",
    //         "wx_secret" => "065d6c62dc43b4427167c155d991135d"
    //     ];
    //     $this->config = $config;
    //     BsPay::init($config, true);
    // }

    /**
     * 汇付天下轮训
     * @param mixed $arr
     * @param mixed $channel
     * @return array
     */
    public function payment($arr = [], $channel)
    {
        //
        try {
            //code...

            $config = json_decode($arr['dataJson'], true);

            $pay_channel_id = $arr['id'];
            $name = $arr['name'];
            Log::channel($channel)->info("通道{$pay_channel_id}:{$name}请求数据:", [$arr]);

            // 收款限制额度
            $amount_limit = $arr['amount_limit'];
            $visitKey = 'user_channel_amount:' . date('d') . 'id:' . $pay_channel_id;
            $currentValue = Redis::get($visitKey);
            $amount = bcadd($currentValue, $arr['amount'], 2);
            if ($amount && $amount_limit && $amount <= $amount_limit) {
                //
                $this->config = $config;
                Log::channel($channel)->info("通道{$pay_channel_id}:{$name}请求返回数据:", ['开始请求']);

                BsPay::init($config, true);
                $data = $this->paymentJspay($arr);
                Log::channel($channel)->info("通道{$pay_channel_id}:{$name}请求返回数据:", ['请求结束']);

                // 写入laravel日志
                Log::channel($channel)->info("通道{$pay_channel_id}:{$name}请求返回数据:", [$data['data']]);
                Log::channel($channel)->info("通道{$pay_channel_id}:{$name}请求返回数据:========== " . $data['data']['resp_desc']);

                if ($data['data']['resp_code'] == '00000100') {
                    if ($arr['tradeType'] == 'A_NATIVE') {
                        //
                        $pageURL = $data['data']['qr_code'];
                    } else if ($arr['tradeType'] == 'T_JSAPI') {
                        //
                        $pageURL = $data['data']['pay_info'];
                    }
                    Log::channel($channel)->info("通道{$pay_channel_id}:{$name}请求返回数据:========== 成功,返回数据");
                    return [
                        "code" => 200,
                        "pay_channel_id" => $pay_channel_id,
                        "pay_url" => $pageURL,
                    ];
                } else if ($data['data']['resp_code'] == '********') {
                    // 没有开通余额支付权限
                    // 通道进行下架处理
                    PayChannel::where('id', $pay_channel_id)->update(['status' => 0, 'edit' => isset($data['data']['bank_message']) ? $data['data']['bank_message'] : '未知原因通道下架']);
                }
            } else {
                Log::channel($channel)->info("通道{$pay_channel_id}:{$name}请求返回数据:========== 通道额度限制");
            }
            Log::channel($channel)->info("通道{$pay_channel_id}:{$name}请求返回数据:========== 下单失败");
            return [
                "code" => 400,
            ];
        } catch (\Throwable $th) {
            //throw $th;
            return [
                "code" => 400,
            ];
        }
    }

    /**
     * 汇付添加收款
     * @param mixed $arr
     * @return array{resp_code: int|string, resp_desc: string[]|bool|mixed|string}
     */
    public function paymentJspay($arr = [])
    {
        try {
            //code...
            // 2.组装请求参数
            $request = new V2TradePaymentJspayRequest();
            // 请求日期
            $request->setReqDate(date("Ymd"));
            // 请求流水号
            $request->setReqSeqId($arr['order_id']);
            // 商户号
            $request->setHuifuId($this->config['huifu_id']);
            // 商品描述
            $request->setGoodsDesc($arr['goods_name']);
            // 交易类型
            $request->setTradeType($arr['tradeType']);
            // 交易金额
            $request->setTransAmt(bcadd($arr['amount'], 0, 2));
            // 设置非必填字段
            self::getWxOpenid($arr['code'], $arr['tradeType'], $request);
            $extendInfoMap = $request->getExtendInfos();
            $request->setExtendInfo($extendInfoMap);
            // 3. 发起API调用
            $client = new BsPayClient();
            $result = $client->postRequest($request);

            if (!$result || $result->isError()) {  //失败处理
                return [
                    'data' => [
                        "resp_code" => "91111119",
                        "resp_desc" => "通道异常，请稍后重试"
                    ]
                ];
            } else {    //成功处理
                return $result->getRspDatas();
            }
        } catch (\Throwable $th) {
            //throw $th;
            return [
                'data' => [
                    "resp_code" => "91111118",
                    "resp_desc" => $th->getMessage()
                ]
            ];
        }
    }

    // 配置微信公众号支付参数
    public function getWxOpenid($code = '', $tradeType = '', $request)
    {
        //
        if ($tradeType == "T_JSAPI" && $code != '') {
            // 2.组装请求参数
            $data = $this->config;
            $appid = $data['wx_appid'];
            $secret = $data['wx_secret'];
            $accessToken = file_get_contents("https://api.weixin.qq.com/sns/oauth2/access_token?appid={$appid}&secret={$secret}&code={$code}&grant_type=authorization_code");
            $accessToken = json_decode($accessToken, true);
            $openid = isset($accessToken['openid']) ? $accessToken['openid'] : '';
            // 微信appID
            $request->setSubAppid($appid);
            // 微信用户openid
            $request->setSubOpenid($openid);
        }
    }

    /**
     * 个人用户基本信息开户
     * @param mixed $bank_id 商户银行卡ID
     * @param mixed $pay_channel_id 供应商通道ID
     * @param mixed $channel 日志地址
     * @return array
     *
     * 接口返回信息 [
     *     "data" => [
     *         "huifu_id" => "****************"
     *         "resp_code" => "********"
     *         "resp_desc" => "成功"
     *     ],
     *     "sign" => "RIxfAc4iy513H9mtukDMHGasr3Lt13vqomly5yOYU179zMN9lK3GOe4+SeDzFBZUgt914bLbZfnLzFv0FZwHZtJyy/syX4wHbaHeiQGqPcRt7LiGfWeeawb2CtHdwQfI2KNRV3uen0O7C5au7kZr+TpZXbXF09AA",
     * ];
     */
    public function userBasicDataIndv($bank_id = 0, $pay_channel_id = 0, $channel = 'bank')
    {
        try {
            //code...
            $dataJson = PayChannel::where('id', $pay_channel_id)->value('dataJson');
            $config = json_decode($dataJson, true);
            $UsersBank = UsersBank::where([
                'id' => $bank_id,
            ])->first();

            if ($UsersBank && $config) {
                //
                $user_id = $UsersBank['user_id'];
                $name = $UsersBank['name'];
                $idcard = $UsersBank['idcard'];
                $num = $UsersBank['num'];
                $cert_begin_date = $UsersBank['cert_begin_date'];
                $mobile_no = $UsersBank['mobile_no'];

                // 查看是否已经认证过了
                $UserBbBankId = UserBbBank::where([
                    'bank_id' => $bank_id,
                    'user_id' => $user_id,
                    'pay_channel_id' => $pay_channel_id,
                    'num' => $num,
                    'name' => $name,
                    'idcard' => $idcard,
                    'attestation' => 2,
                ])->value('id');
                if ($UserBbBankId) {
                    //
                    return [
                        'bb_bank_id' => $UserBbBankId,
                        'status' => 2,
                        'msg' => '认证成功1',
                    ];
                }

                BsPay::init($config, true);
                // 2.组装请求参数
                $request = new V2UserBasicdataIndvRequest();
                // 请求流水号
                $request->setReqSeqId(date("YmdHis") . mt_rand());
                // 请求日期
                $request->setReqDate(date("Ymd"));
                // 个人姓名
                $request->setName($name);
                // 个人证件类型
                $request->setCertType("00");
                // 个人证件号码
                $request->setCertNo($idcard);
                // 个人证件有效期类型
                $request->setCertValidityType("1");
                // 个人证件有效期开始日期
                $request->setCertBeginDate($cert_begin_date);
                // 手机号
                $request->setMobileNo($mobile_no);
                // 3. 发起API调用
                $client = new BsPayClient();
                $result = $client->postRequest($request);
                $id = '';
                if (!$result || $result->isError()) {  //失败处理
                    $status = 3;
                    $msg = '认证失败';
                    Log::channel($channel)->info("通道{$pay_channel_id} 开户信息{$bank_id}:" . '开户失败1');
                } else {
                    //成功处理
                    $data = $result->getRspDatas();

                    if ($data['data']['resp_code'] == "********") {
                        $status = 2;
                        $msg = '认证成功2';
                        // 添加待绑定银行卡信息
                        $UserBbBank = UserBbBank::updateOrCreate([
                            'bank_id' => $bank_id,
                            'user_id' => $user_id,
                            'pay_channel_id' => $pay_channel_id,
                            'num' => $num,
                            'name' => $name,
                            'idcard' => $idcard,
                        ], [
                            'user_id' => $user_id,
                            'bank_id' => $bank_id,
                            'pay_channel_id' => $pay_channel_id,
                            'num' => $num,
                            'name' => $name,
                            'status' => 2,
                            'idcard' => $idcard,
                            'huifu_id' => $data['data']['huifu_id'],
                        ]);
                        $id = $UserBbBank->id;
                        Log::channel($channel)->info("通道{$pay_channel_id} 开户信息{$bank_id}:" . '开户成功');
                    } else {
                        $status = 3;
                        $msg = $data['data']['resp_desc'];
                        Log::channel($channel)->info("通道{$pay_channel_id} 开户信息{$bank_id}:" . '开户失败2', [$data['data']['resp_desc']]);
                    }
                }
                return [
                    'bb_bank_id' => $id,
                    'status' => $status,
                    'msg' => $msg,
                ];
            }
            Log::channel($channel)->info("通道{$pay_channel_id} 开户信息{$bank_id}:" . '开户信息不存在');

            return [
                'status' => 3,
                'msg' => '开户信息不存在',
            ];

        } catch (\Throwable $th) {
            //throw $th;
            Log::channel($channel)->info("通道{$pay_channel_id} 开户信息{$bank_id}:" . '开户请求异常', [$th->getMessage(), $th->getLine()]);
            return [
                'status' => 3,
                'msg' => '开户请求异常',
            ];
        }
    }

    /**
     * 用户业务入驻
     * @param mixed $bb_bank_id 供应商银行卡ID
     * @param mixed $channel 日志地址
     *
     * 接口返回数据 [
     *     "data" => [
     *         "huifu_id" => "****************",
     *         "resp_business" => '[{"code":"S","type":"1"},{"code":"S","type":"2"}]',
     *         "resp_code" => "********",
     *         "resp_desc" => "成功",
     *         "token_no" => "***********",
     *     ],
     *     "sign" => "RIxfAc4iy513H9mtukDMHGasr3Lt13vqomly5yOYU179zMN9lK3GOe4+SeDzFBZUgt914bLbZfnLzFv0FZwHZtJyy/syX4wHbaHeiQGqPcRt7LiGfWeeawb2CtHdwQfI2KNRV3uen0O7C5au7kZr+TpZXbXF09AA",
     * ];
     */
    public function userBusiOpen($bb_bank_id = 0, $channel = 'bank')
    {
        $UserBbBank = UserBbBank::where([
            'id' => $bb_bank_id,
            'status' => 2,
        ])->first();
        $pay_channel_id = isset($UserBbBank['pay_channel_id']) ? $UserBbBank['pay_channel_id'] : '';
        $dataJson = PayChannel::where('id', $pay_channel_id)->value('dataJson');
        $config = json_decode($dataJson, true);
        try {
            if ($UserBbBank && $config) {
                // 绑定状态
                $attestation = $UserBbBank->attestation;
                if ($attestation == 2) {
                    // 绑定成功
                    return [
                        'status' => 2,
                        'token_no' => $UserBbBank['token_no'],
                        'msg' => '绑定成功',
                    ];
                }

                $name = $UserBbBank['name'];
                $num = $UserBbBank['num'];

                BsPay::init($config, true);
                //  2.组装请求参数
                $request = new V2UserBusiOpenRequest();
                // 汇付ID
                $request->setHuifuId($UserBbBank['huifu_id']);
                // 请求流水号
                $request->setReqSeqId(date("YmdHis") . mt_rand());
                // 请求日期
                $request->setReqDate(date("Ymd"));
                // 渠道商/商户汇付Id
                $request->setUpperHuifuId($config['huifu_id']);

                $dto = array();
                // 卡类型
                $dto["card_type"] = "1";
                // 卡户名
                $dto["card_name"] = $name;
                // 卡号
                $dto["card_no"] = $num;
                // 银行所在省
                $dto["prov_id"] = "310000";
                // 银行所在市
                $dto["area_id"] = "310100";

                // $dto["cert_type"] = "00";
                // $dto["cert_no"] = $idcard;
                // $dto["cert_validity_type"] = 1;
                // $dto["cert_begin_date"] = $cert_begin_date;

                $card_info = json_encode($dto, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

                $cash_config = json_encode([
                    [
                        // 'fix_amt' => "0.00",
                        // 'fee_rate' => $config['fee_rate'],
                        'cash_type' => "DM",
                    ]
                ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                // 设置非必填字段
                $extendInfoMap = [
                    'card_info' => $card_info,
                    'cash_config' => $cash_config
                ];
                $request->setExtendInfo($extendInfoMap);

                // 3. 发起API调用
                $client = new BsPayClient();
                $result = $client->postRequest($request);
                $token_no = '';
                if (!$result || $result->isError()) {  //失败处理
                    $status = 3;
                    $UserBbBank->attestation = $status;
                    $UserBbBank->msg = '绑定失败';
                } else {
                    //成功处理
                    $data = $result->getRspDatas();
                    if ($data['data']['resp_code'] == "********") {
                        $status = 2;
                        $UserBbBank->attestation = 2;
                        $UserBbBank->msg = '绑定成功';
                        $token_no = $data['data']['token_no'];
                        $UserBbBank->token_no = $token_no;
                        $UserBbBank->save();
                        Log::channel($channel)->info("通道{$pay_channel_id} 开户信息{$bb_bank_id}:" . '绑定成功', [$token_no]);
                        return [
                            'status' => $status,
                            'token_no' => $token_no,
                            'msg' => $UserBbBank->msg,
                        ];
                    } else {
                        $status = 3;
                        $msg = $data['data']['resp_desc'];
                        $UserBbBank->attestation = 3;
                        $UserBbBank->msg = $msg;
                    }
                }
                $UserBbBank->save();
                Log::channel($channel)->info("通道{$pay_channel_id} 开户信息{$bb_bank_id}:" . '绑定失败', [$UserBbBank->msg]);
                return [
                    'status' => $status,
                    'token_no' => $token_no,
                    'msg' => $UserBbBank->msg,
                ];
            }
            Log::channel($channel)->info("通道{$pay_channel_id} 开户信息{$bb_bank_id}:" . '绑定信息不存在');
            return [
                'status' => 3,
                'msg' => '绑定信息不存在',
            ];
        } catch (\Throwable $th) {
            //throw $th;
            $UserBbBank->attestation = 3;
            $UserBbBank->msg = $th->getMessage();
            $UserBbBank->save();
            Log::channel($channel)->info("通道{$pay_channel_id} 开户信息{$bb_bank_id}:" . '绑定请求异常', [$th->getMessage(), $th->getLine()]);

            return [
                'status' => 3,
                'msg' => $th->getMessage(),
            ];
        }
    }

    /**
     * 商户余额转账到用户
     * [
     *    "data" => [
     *        "acct_split_bunch" => '{"acct_infos":[{"acct_id":"F03717122","div_amt":"5.01","huifu_id":"****************"}],"fee_acct_id":"F03643629","fee_amt":"0.50","fee_huifu_id":"****************"}',
     *        "hf_seq_id" => "003500TOP2A241030155351P534ac139cfe00000",
     *        "ord_amt" => "5.01",
     *        "out_huifu_id" => "****************",
     *        "req_date" => "********",
     *        "req_seq_id" => "**************1279511508",
     *        "resp_code" => "********",
     *        "resp_desc" => "交易成功",
     *        "trans_finish_time" => "**************",
     *        "trans_stat" => "S",
     *    ]
     *];
     * @return array
     */
    public function paymentTradePay($amount = 0, $recharge_list_order, $bb_bank_id, $config)
    {

        BsPay::init($config, true);
        $UserBbBank = UserBbBank::where(['id' => $bb_bank_id, 'attestation' => 2])->first();

        if ($UserBbBank) {
            // 2.组装请求参数
            $request = new V2TradeAcctpaymentPayRequest();
            // 请求流水号
            $request->setReqSeqId($recharge_list_order);
            // 请求日期
            $request->setReqDate(date("Ymd"));
            // 出款方商户号
            $request->setOutHuifuId($config['huifu_id']);
            // 支付金额
            $request->setOrdAmt($amount);
            // 分账对象
            $getAcctSplitBunch = json_encode([
                "acct_infos" => [
                    "huifu_id" => $UserBbBank['huifu_id'],
                    "div_amt" => $amount
                ]
            ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            $request->setAcctSplitBunch($getAcctSplitBunch);
            // 安全信息
            $request->setRiskCheckData('{"transfer_type":"05"}');

            // 3. 发起API调用
            $client = new BsPayClient();
            $result = $client->postRequest($request);

            if (!$result || $result->isError()) {  //失败处理
                return [
                    "resp_code" => "400",
                    "resp_desc" => "通道异常，请稍后重试"
                ];
            } else {    //成功处理
                $data = $result->getRspDatas();
                Log::channel('recharge')->info("通道：{$UserBbBank['pay_channel_id']}---开始下发申请：{$recharge_list_order}---金额：{$amount}---返回信息", $data);

                $resp_code = $data['data']['resp_code'];
                $resp_desc = $data['data']['resp_desc'];

                if ($resp_code == '********') {
                    // 余额转账成功
                    return [
                        "resp_code" => "********",
                        "resp_desc" => "余额转账成功"
                    ];
                    // return self::tradeSettlementEnchashment(['amount' => $amount, "huifu_id" => $UserBbBank['huifu_id'], "token_no" => $UserBbBank['token_no']], $config, $recharge_list_order);
                }
                // 余额转账失败
                return [
                    "resp_code" => "400",
                    "resp_desc" => $resp_desc
                ];
            }
        }
        // 此通道无用户业务入驻信息
        return [
            "resp_code" => "400",
            "resp_desc" => '此通道无用户业务入驻信息'
        ];
    }

    /**
     * 取现
     * @param mixed $arr
     * @return array
     *
     * 接口返回数据 [
     *     "data" => [
     *         "acct_id" => "F03717122",
     *         "hf_seq_id" => "003000TOP1B241104121406P946ac139ffb00000",
     *         "huifu_id" => "****************",
     *         "req_date" => "********",
     *         "req_seq_id" => "********121406872263934",
     *         "resp_code" => "********",
     *         "resp_desc" => "受理成功",
     *         "trans_stat" => "P",
     *     ],
     *     "sign" => "YTfPXwYgL7xlYvRoNeDXW4hF0i9NFd9dbUDYGHdb4DhXg6+riMhkZvWkaRuufrF8wgV0x5sZWboKBatLHQL2NJu5Q+cwdhPE+ZNf1OzA99kJlhhvg90efu+hHfxfEw3g88UI9igP12fOQpMPY64zdRIh3lEj+/6C"
     * ];
     */
    public function tradeSettlementEnchashment($arr = ['amount' => "0.00", "huifu_id" => "", "token_no" => ""], $config, $recharge_list_order)
    {
        BsPay::init($config, true);
        // 2.组装请求参数
        $request = new V2TradeSettlementEnchashmentRequest();
        // 请求日期
        $request->setReqDate(date("Ymd"));
        // 请求流水号
        $request->setReqSeqId($recharge_list_order);
        // 取现金额
        $request->setCashAmt($arr['amount']);
        // 取现方ID号
        $request->setHuifuId($arr['huifu_id']);
        // 到账日期类型
        $request->setIntoAcctDateType("DM");
        // 取现卡序列号
        $request->setTokenNo($arr['token_no']);

        // 设置非必填字段
        $extendInfoMap = [
            'notify_url' => rtrim(env('APP_URL'), '/') . '/api/recharge/notice/huifu'
        ];
        $request->setExtendInfo($extendInfoMap);

        // 3. 发起API调用
        $client = new BsPayClient();
        $result = $client->postRequest($request);
        if (!$result || $result->isError()) {  //失败处理
            return [
                "resp_code" => "400",
                "resp_desc" => "通道异常，请稍后重试",
                "trans_stat" => 'F',
            ];
        } else {
            //成功处理
            $data = $result->getRspDatas();
            Log::channel('recharge')->info("---开始取现申请：{$recharge_list_order}---金额：{$arr['amount']}---返回信息", $data);
            $resp_code = $data['data']['resp_code'];
            $resp_desc = $data['data']['resp_desc'];
            $trans_stat = $data['data']['trans_stat'];

            if ($resp_code == '********') {
                // 取现成功
                return [
                    "resp_code" => "********",
                    "resp_desc" => $resp_desc,
                    "trans_stat" => $trans_stat,
                ];
            }
            // 取现失败
            return [
                "resp_code" => "400",
                "resp_desc" => $resp_desc,
                "trans_stat" => 'F',
            ];
        }
    }

    /**
     * 获取余额查询
     * @param mixed $channel_id 通道ID
     * @param mixed $channel 日志地址
     * @return array
     *
     * 请求返回数据[
     *   'balance_amt' => 0, 账户余额
     *   'avl_bal' => 0, 可用余额
     * ]
     *
     * 接口返回数据 [
     *     "data" => [
     *         "acctInfo_list" => '[{"acct_id":"F03643629","acct_stat":"N","acct_type":"01","avl_bal":"192.31","balance_amt":"192.31","frz_bal":"0.00","huifu_id":"****************","last_avl_bal":"192.31","un_transit_bal":"192.31"}]',
     *         "req_date" => "********",
     *         "resp_code" => "********",
     *         "resp_desc" => "查询成功",
     *     ],
     *     "sign" => "FdCdtqAHYUoLpyezM+YTWv1x37O23BM41ejKHaPocTOy+MltL2a3ylPFpEEvylrqcDrOSf+EbXknloqZMqN8nMHcaGtCKPEmsqBGLkrkbAot/kmjkMwMc6pLcQpV+yNS3BLjx+zchBDX3esVbJo6iZz6au3mDccS "
     * ];
     */
    public function tradeAcctPaymentBalanceQuery($channel_id, $channel = 'bank')
    {
        $PayChannel = PayChannel::where('id', $channel_id)->first();
        if ($PayChannel) {
            $config = json_decode($PayChannel->dataJson, true);
            BsPay::init($config, true);
            //  2.组装请求参数
            $request = new V2TradeAcctpaymentBalanceQueryRequest();
            // 请求日期
            $request->setReqDate(date("Ymd"));
            // 商户号
            $request->setHuifuId($config['huifu_id']);

            // 3. 发起API调用
            $client = new BsPayClient();
            $result = $client->postRequest($request);
            if (!$result || $result->isError()) {  //失败处理
                return [
                    'balance_amt' => 0,
                    'avl_bal' => 0,
                ];
            } else {
                //成功处理
                $data = $result->getRspDatas();
                if ($data['data']['resp_code'] == "********") {
                    $acctInfo_list = json_decode($data['data']['acctInfo_list'], true);
                    $balance_amt = isset($acctInfo_list[0]['balance_amt']) ? $acctInfo_list[0]['balance_amt'] : 0;
                    $avl_bal = isset($acctInfo_list[0]['avl_bal']) ? $acctInfo_list[0]['avl_bal'] : 0;
                    // 同步供应商余额
                    $PayChannel->balance_amt = $balance_amt;
                    $PayChannel->avl_bal = $avl_bal;
                    $PayChannel->updated_at = now();
                    $PayChannel->save();
                    Log::channel($channel)->info("通道{$channel_id} 余额查询成功", [$acctInfo_list]);
                    return [
                        'balance_amt' => $balance_amt,  // 账户余额
                        'avl_bal' => $avl_bal, // 可用余额
                    ];
                }
            }
        }
        return [
            'balance_amt' => 0,
            'avl_bal' => 0,
        ];
    }

    public function tradeAcctPaymentScanpayQueryArr()
    {
        $PayChannelArr = PayChannel::where('channel_type', 1)->pluck('id')->toArray();
        $data = Order::where('status', 1)->whereIn('pay_channel_id', $PayChannelArr)->get(['pay_channel_id', 'order_id', 'created_at'])->toArray();
        foreach ($data as $key => $value) {
            $formattedDate = Carbon::parse($value['created_at'])->format('Ymd');
            $this->tradeAcctPaymentScanpayQuery($value['pay_channel_id'], $value['order_id'], $formattedDate);
            //
        }
    }

    /**
     * 查询订单状态
     * @param mixed $channel_id
     * @param mixed $channel
     */
    public function tradeAcctPaymentScanpayQuery($channel_id, $order_id, $date)
    {
        $log = 'order_notify';
        $PayChannel = PayChannel::where('id', $channel_id)->first();

        if ($PayChannel) {
            $config = json_decode($PayChannel->dataJson, true);

            BsPay::init($config, true);
            //  2.组装请求参数
            $request = new V2TradePaymentScanpayQueryRequest();
            // 商户号
            $request->setHuifuId($config['huifu_id']);
            $request->setOrgReqSeqId($order_id);
            $request->setOrgReqDate($date);
            // 3. 发起API调用
            $client = new BsPayClient();
            $result = $client->postRequest($request);

            if (!$result || $result->isError()) {  //失败处理
                return '';
            } else {
                //成功处理
                $data = $result->getRspDatas();
                Log::channel($log)->info('收款回调数据', [$data]);
                if ($data['data']['resp_code'] == "********") {
                    $resp_data = $data['data'];
                    $trans_status = $resp_data['trans_stat'];

                    if (isset($resp_data) && in_array($trans_status, ['S', 'F'])) {
                        $status = 4; // 失败
                        if ($trans_status == 'S') {
                            // 成功
                            $status = 2;
                        }

                        $orderNum = isset($resp_data['org_req_seq_id']) ? $resp_data['org_req_seq_id'] : '';
                        $tradeNo = isset($resp_data['org_hf_seq_id']) ? $resp_data['org_hf_seq_id'] : '';
                        $retMsg = isset($resp_data['bank_desc']) ? $resp_data['bank_desc'] : '下发失败';
                        Log::channel($log)->info('收款回调--', [$orderNum, $status]);

                        $arr = [
                            'orderNo' => $orderNum,
                            'tradeNo' => $tradeNo,
                            'status' => $status,
                            'retMsg' => $retMsg,
                        ];

                        (new NotifyController())->orderNoticePayHandle($arr, 'huifu');
                        echo $retMsg . '/r/n';
                    }
                }
            }
        }
    }

    /**
     * 刷新余额
     * @return array{code: int, msg: string}
     */
    public function refreshBalances()
    {
        $PayChannel = PayChannel::whereIn('channel_type', [1])->get();
        foreach ($PayChannel as $key => $value) {
            if ($value->channel_type == 1) {
                self::tradeAcctPaymentBalanceQuery($value->id);
            }
        }
        return [
            'code' => 200,
            'msg' => '刷新成功',
        ];
    }
}
