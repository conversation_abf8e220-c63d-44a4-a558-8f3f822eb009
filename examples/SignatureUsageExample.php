<?php

/**
 * Signature Usage Examples
 * 
 * This file demonstrates how to use the App\Utils\Signature class
 * for various signature generation and verification scenarios.
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Utils\Signature;

echo "=== Signature Usage Examples ===\n\n";

// Example 1: Basic signature generation (hex format)
echo "1. Basic Hex Signature Generation:\n";
$timestamp = '1631257823000';
$nonce = 'abcd1234';
$body = 'the post request body content';
$secretKey = 'JX7BzFrIH2ZA-vR2qJneTSmrf_LxdMkS960PgKHQsqE=';

$hexSignature = Signature::verifySignature($timestamp, $nonce, $body, $secretKey);
echo "Timestamp: $timestamp\n";
echo "Nonce: $nonce\n";
echo "Body: $body\n";
echo "Secret Key: $secretKey\n";
echo "Hex Signature: $hexSignature\n\n";

// Example 2: Base64 signature generation
echo "2. Base64 Signature Generation:\n";
$base64Signature = Signature::generateSignature($timestamp, $nonce, $body, $secretKey);
echo "Base64 Signature: $base64Signature\n\n";

// Example 3: Signature verification
echo "3. Signature Verification:\n";
$isValid = Signature::verifyBase64Signature($timestamp, $nonce, $body, $base64Signature, $secretKey);
echo "Is signature valid? " . ($isValid ? 'YES' : 'NO') . "\n";

$wrongSignature = 'wrong_signature_here';
$isInvalid = Signature::verifyBase64Signature($timestamp, $nonce, $body, $wrongSignature, $secretKey);
echo "Is wrong signature valid? " . ($isInvalid ? 'YES' : 'NO') . "\n\n";

// Example 4: Hex signature verification
echo "4. Hex Signature Verification:\n";
$payload = sprintf("%s\n%s\n%s\n", $timestamp, $nonce, $body);
$hexSig = Signature::sign($payload, $secretKey);
$verifyResult = Signature::verify($payload, $hexSig, $secretKey);
echo "Payload: " . str_replace("\n", "\\n", $payload) . "\n";
echo "Hex Signature: $hexSig\n";
echo "Verification result: " . ($verifyResult === null ? 'VALID' : $verifyResult) . "\n\n";

// Example 5: Generate random nonce and current timestamp
echo "5. Generate Random Nonce and Current Timestamp:\n";
$randomNonce = Signature::generateNonce();
$currentTimestamp = Signature::getCurrentTimestamp();
echo "Random Nonce (16 chars): $randomNonce\n";
echo "Current Timestamp (ms): $currentTimestamp\n";

$customNonce = Signature::generateNonce(32);
echo "Custom Nonce (32 chars): $customNonce\n\n";

// Example 6: Real-world API request simulation
echo "6. Real-world API Request Simulation:\n";
$apiTimestamp = Signature::getCurrentTimestamp();
$apiNonce = Signature::generateNonce(16);
$apiBody = json_encode([
    'user_id' => '12345',
    'amount' => '100.00',
    'currency' => 'USD',
    'order_id' => 'ORD-' . time()
]);
$apiSecretKey = 'your-api-secret-key-here';

echo "API Request Data:\n";
echo "Timestamp: $apiTimestamp\n";
echo "Nonce: $apiNonce\n";
echo "Body: $apiBody\n";

$apiSignature = Signature::generateSignature($apiTimestamp, $apiNonce, $apiBody, $apiSecretKey);
echo "Generated Signature: $apiSignature\n";

// Simulate verification on the receiving end
$verificationResult = Signature::verifyBase64Signature($apiTimestamp, $apiNonce, $apiBody, $apiSignature, $apiSecretKey);
echo "Verification Result: " . ($verificationResult ? 'VALID' : 'INVALID') . "\n\n";

// Example 7: Error handling demonstration
echo "7. Error Handling:\n";
try {
    $invalidSignature = Signature::generateSignature('', '', '', '');
} catch (Exception $e) {
    echo "Caught expected error: " . $e->getMessage() . "\n";
}

try {
    $result = Signature::verify('test', 'invalid_hex', 'key');
    echo "Invalid hex verification result: " . ($result === null ? 'VALID' : $result) . "\n";
} catch (Exception $e) {
    echo "Caught error: " . $e->getMessage() . "\n";
}

echo "\n=== End of Examples ===\n";
