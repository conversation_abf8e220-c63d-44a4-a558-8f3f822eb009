<?php

use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;
if (!function_exists('generateOrderNumber')) {
    /**
     * 生成18位订单号
     * $length：随机数长度
     */
    function generateOrderNumber($datacenter = -1, $workerId = -1)
    {
        $order_no = date('ymdHis');
        $millisecond = substr(microtime(), 2, 3);
        $datacenter = $datacenter > 31 || $datacenter < 0 ? mt_rand(0, 31) : $datacenter;
        $workerId = $workerId > 31 || $workerId < 0 ? mt_rand(0, 31) : $workerId;
        $workerLength = 5;
        $workerLeftMoveLength = 12;
        $datacenterLeftMoveLength = $workerLeftMoveLength + $workerLength;
        $timestampLeftMoveLength = $datacenterLeftMoveLength + $workerLength;
        $ext = (string) (((intval($millisecond)) << $timestampLeftMoveLength) | ($datacenter << $datacenterLeftMoveLength) | ($workerId << $workerLeftMoveLength) | (
            mt_rand(
                0,
                4095
            )
        ));
        $order_no .= str_pad($ext, 10, '0', STR_PAD_LEFT);

        return $order_no;
    }
}

if (!function_exists('userChannelAmount')) {
    // 增加通道每日收款额度
    function userChannelAmount($pay_channel_id, $amount, $boole)
    {
        try {
            $visitKey = 'user_channel_amount:' . date('d') . 'id:' . $pay_channel_id;
            // 设置过期时间为当天结束
            $time = AmTdoa();

            if ($boole == 'add') {
                // 如果键存在，累加值
                Redis::incrby($visitKey, $amount);
                // 重新设置过期时间
                Redis::expire($visitKey, $time);
            }
            // 尝试获取当前值并累加
            // $currentValue = Redis::get($visitKey);

        } catch (\Throwable $th) {
            return true;
        }

    }
}

if (!function_exists('AmTdoa')) {
    // 获取距离第二天凌晨有多少秒
    function AmTdoa()
    {
        $date = date('Y-m-d', strtotime('+1 day'));
        $carbon = Carbon::parse($date);
        $int = (new Carbon)->diffInSeconds($carbon, false);
        return $int;
    }
}

if (!function_exists('IdCardRandomDate')) {
    /**
     * 根据身份证号 513822199708104215，我们可以提取出生日期部分 19970810，
     * 即 1997 年 8 月 10 日。为了生成一个合理的个人证件有效期开始日期，
     * 我们可以选择从该人的成年年龄（通常是 18 岁）开始的一个随机日期。
     * @param mixed $identityCard
     * @return string
     */
    function IdCardRandomDate($identityCard)
    {
        // 提取出生日期
        $birthDate = substr($identityCard, 6, 8);
        $birthYear = intval(substr($birthDate, 0, 4));
        $birthMonth = intval(substr($birthDate, 4, 2));
        $birthDay = intval(substr($birthDate, 6, 2));

        // 计算成年日期（18岁）
        $adultYear = $birthYear;
        $adultDate = new DateTime("$adultYear-$birthMonth-$birthDay");

        // 生成一个随机的有效期开始日期（在成年后的 1 到 10 年内）
        $randomYears = mt_rand(1, 10);
        $startDate = clone $adultDate;
        $startDate->modify("+$randomYears years");

        // 格式化日期
        $formattedStartDate = $startDate->format('Ymd');
        return $formattedStartDate;
    }
}

if (!function_exists('generateRandomPhoneNumber')) {
    /**
     * 随机生成，中国的手机号码通常以 13、15、17、18、19 开头，且总长度为 11 位数字。
     * @return string
     */
    function generateRandomPhoneNumber()
    {
        // 定义手机号码前缀
        $prefixes = ['13', '15', '17', '18', '19'];

        // 随机选择一个前缀
        $prefix = $prefixes[array_rand($prefixes)];

        // 生成剩余的 9 位随机数字
        $suffix = '';
        for ($i = 0; $i < 9; $i++) {
            $suffix .= mt_rand(0, 9);
        }

        // 组合成完整的手机号码
        $phoneNumber = $prefix . $suffix;
        return $phoneNumber;
    }
}

if (!function_exists('getBankByBin')) {
    /**
     * 根据银行卡号返回归属银行和地区
     * https://www.juhe.cn/box/index/id/305/1137
     * @return array
     * "bankcard":"6213361739910693877",
     * "bankname":"农业银行",
     * "abbreviation":"ABC",
     * "cardtype":"借记卡",
     * "nature":"专用惠农卡",
     * "province":"河北省",
     * "city":"沧州",
     * "card_bin":"621336",
     * "bin_digits":6,
     * "card_digits":19,
     * "isLuhn":true,
     * "banklogo":"http:\/\/bkaear.market.alicloudapi.com\/banklogo\/nongyeyinhang.gif",
     * "weburl":"http:\/\/www.abchina.com\/",
     * "kefu":"95599"
     * 
     */
    function getBankByBin($bankcard = '')
    {
        try {
            //code...
            return Http::get(
                'http://apis.juhe.cn/bankcardcore/query',
                [
                    'bankcard' => $bankcard,
                    'key' => 'd51d87da0af9ee613b1da46384f6a9fe'
                ]
            )->json('result');
        } catch (\Throwable $th) {
            //throw $th;
            return [];
        }
    }
}
