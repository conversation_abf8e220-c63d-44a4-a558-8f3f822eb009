<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->nullable()->comment('名称');
            $table->string('agent_id')->nullable()->comment('商户号');
            $table->string('key')->nullable()->comment('密钥');
            $table->decimal('withdraw_amount')->default('0.00')->comment('提现金额');
            $table->decimal('recharge_amount')->default('0.00')->comment('充值金额');
            $table->decimal('freeze_amount')->default('0.00')->comment('冻结金额');
            $table->decimal('credit_amount')->default('0.00')->nullable()->comment('授信金额');
            $table->longText('edit')->nullable()->comment('备注');
            $table->integer('status')->default('0')->nullable()->comment('0下架 1上架 2异常');
            $table->string('recharge_rate_id')->nullable()->comment('代付费率');
            $table->decimal('min_amount_o')->default('0.00')->comment('单笔收款最小金额');
            $table->decimal('max_amount_o')->default('0.00')->comment('单笔收款最大金额');
            $table->decimal('min_amount_r')->default('0.00')->comment('单笔提现最小金额');
            $table->decimal('max_amount_r')->default('0.00')->comment('单笔提现最大金额');
            $table->integer('max_recharge_count')->default('0')->comment('每日提现次数');
            $table->decimal('recharge_point')->default('0.00')->comment('充值点位%');
            $table->integer('type')->default('1')->nullable()->comment('商户类型  1人民部 2 虚拟部');
            $table->integer('agent_status')->default('0')->nullable()->comment('0关闭 1开启');
            $table->integer('max_reg_amount')->default('0')->comment('每日代付最大金额');
            $table->time('recharge_start_time')->nullable();
            $table->time('recharge_end_time')->nullable();
            $table->time('order_start_time')->nullable();
            $table->time('order_end_time')->nullable();
            $table->string('trade_type')->nullable()->comment('收款方式');
            $table->string('pay_channel_id')->nullable()->comment('通道ID');
            $table->string('relationship_id')->nullable()->comment('绑定商户，A划转到B商户');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users');
    }
}
