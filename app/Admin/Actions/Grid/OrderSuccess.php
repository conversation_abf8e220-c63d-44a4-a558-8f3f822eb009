<?php
/*
 * 手动付款
 */

namespace App\Admin\Actions\Grid;

use Dcat\Admin\Admin;
use Dcat\Admin\Grid\RowAction;
use App\Admin\Actions\Forms\OrderSuccess as Form;

class OrderSuccess extends RowAction
{
  public function render()
  {
    $id = "OrderSuccess-{$this->getKey()}";

    // 模态窗
    $this->modal($id);

    return <<<HTML
<span class="grid-expand" data-toggle="modal" data-target="#{$id}">
   <a href="javascript:void(0)"><i class="fa fa-hand-lizard-o"></i> 手动处理订单
</a>
</span>
<li class="dropdown-divider"></li>

HTML;
  }

  protected function modal($id)
  {
    // 在弹窗标题处显示当前行的用户名
    $name = $this->row->order_id;
    // 工具表单
    $form = new Form($this->row);

    // 刷新页面时移除模态窗遮罩层
    // 从 v1.5.0 版本开始可以移除这段 JS 代码
    Admin::script('Dcat.onPjaxComplete(function () {
            $(".modal-backdrop").remove();
            $("body").removeClass("modal-open");
        }, true)');

    // 通过 Admin::html 方法设置模态窗HTML
    Admin::html(
      <<<HTML
<div class="modal fade" id="{$id}">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">{$name}-手动处理订单</h4>
         <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
      </div>
      <div class="modal-body">
        {$form->render()}
      </div>
    </div>
  </div>
</div>
HTML
    );
  }
}
