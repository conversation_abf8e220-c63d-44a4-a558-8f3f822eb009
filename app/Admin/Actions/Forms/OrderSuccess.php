<?php
/*
 * @Author: your name
 * @Date: 2021-03-04 14:16:23
 * @LastEditTime: 2021-07-07 10:58:27
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \huoke\app\Admin\Actions\Forms\Order\Assistant.php
 */

namespace App\Admin\Actions\Forms;

use Exception;
use App\Models\Order;
use Dcat\Admin\Widgets\Form;
use Dcat\Admin\Traits\LazyWidget;
use Illuminate\Support\Facades\Log;
use Dcat\Admin\Contracts\LazyRenderable;
use App\Http\Controllers\NotifyController;

class OrderSuccess extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input)
    {
        // 程序逻辑处理：
        try {
            // 获取外部传递的参数
            $status = $input['status'];
            $batch_no = $input['order_id'];

            $Order = Order::where('order_id', $batch_no)->where('status', 1)->first(['id', 'amount', 'user_id', 'amount_san']);
            if (!$Order) {
                // 订单不存在
                throw new Exception('订单不存在:' . $batch_no);
            }

            $detail_data_msg = '手动处理成功';
            if ($status == 4) {
                $detail_data_msg = '手动处理失败';
            }

            $arr = [
                'orderNo' => $batch_no,
                'tradeNo' => '',
                'status' => $status,
                'retMsg' => $detail_data_msg,
            ];
            $log = 'order_notify';
            Log::channel($log)->info('收款回调数据', [$arr]);
            Log::channel($log)->info('收款回调--', [$batch_no, $status]);

            (new NotifyController())->orderNoticePayHandle($arr, 'huifu');

            return $this->response()->success('处理成功')->refresh();
        } catch (Exception $th) {
            //throw $th;
            // 释放锁
            return $this->response()->error($th->getMessage());
        }
    }

    public function form()
    {
        $this->html('<div class="col-md-12"><h4 style="color:#a93232">处理订单状态</h4><hr></div>');
        $this->html('<div class="col-md-12"><h6 style="color:#a93232">该功能请不要随意测试使用。功能作用：上游回调后没有及时处理，需要人工去处理该笔订单 成功或者失败。</h6><hr></div>');
        $this->hidden('batch_amt');
        $this->radio('status', '订单状态')->options([2 => '成功', 4 => '失败'])->required();
        $this->hidden('order_id');
    }

    /**
     * 默认信息
     *
     * @return void
     */
    public function default()
    {
        return;
    }
}
